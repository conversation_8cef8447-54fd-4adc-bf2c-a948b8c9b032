from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate, logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.views import (
    LoginView, LogoutView, PasswordResetView, PasswordResetDoneView,
    PasswordResetConfirmView, PasswordResetCompleteView, PasswordChangeView,
    PasswordChangeDoneView
)
from django.contrib import messages
from django.urls import reverse_lazy, reverse
from django.http import HttpResponseRedirect
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.contrib.auth.models import User

from .forms import (
    CustomUserRegistrationForm, CustomAuthenticationForm, CustomPasswordChangeForm,
    CustomPasswordResetForm, UserProfileForm, EmailVerificationForm
)
from .models import UserProfile, EmailVerificationToken, LoginAttempt


def get_client_ip(request):
    """Get client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def register_view(request):
    """User registration view"""
    if request.user.is_authenticated:
        return redirect('zhid_auth:profile')

    if request.method == 'POST':
        form = CustomUserRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save()

            # Create email verification token
            verification_token = EmailVerificationToken.objects.create(user=user)
            verification_token.send_verification_email()

            # Log the registration
            LoginAttempt.objects.create(
                user=user,
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                success=True,
                email_attempted=user.email
            )

            messages.success(
                request,
                'Registration successful! Please check your email to verify your account.'
            )
            return redirect('zhid_auth:login')
    else:
        form = CustomUserRegistrationForm()

    return render(request, 'zhid_auth/register.html', {'form': form})


class CustomLoginView(LoginView):
    """Custom login view"""
    form_class = CustomAuthenticationForm
    template_name = 'zhid_auth/login.html'
    redirect_authenticated_user = True

    def get_success_url(self):
        return reverse_lazy('zhid_auth:profile')

    def form_valid(self, form):
        # Log successful login
        LoginAttempt.objects.create(
            user=form.get_user(),
            ip_address=get_client_ip(self.request),
            user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
            success=True,
            email_attempted=form.get_user().email
        )

        messages.success(self.request, f'Welcome back, {form.get_user().first_name or form.get_user().username}!')
        return super().form_valid(form)

    def form_invalid(self, form):
        # Log failed login attempt
        username = form.cleaned_data.get('username', '')
        email_attempted = username if '@' in username else ''

        LoginAttempt.objects.create(
            ip_address=get_client_ip(self.request),
            user_agent=self.request.META.get('HTTP_USER_AGENT', ''),
            success=False,
            email_attempted=email_attempted
        )

        messages.error(self.request, 'Invalid username/email or password.')
        return super().form_invalid(form)


class CustomLogoutView(LogoutView):
    """Custom logout view"""
    template_name = 'zhid_auth/logout.html'

    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            messages.success(request, 'You have been successfully logged out.')
        return super().dispatch(request, *args, **kwargs)


@login_required
def profile_view(request):
    """User profile view"""
    try:
        profile = request.user.profile
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(user=request.user)

    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=profile, user=request.user)
        if form.is_valid():
            # Update user fields
            request.user.first_name = form.cleaned_data['first_name']
            request.user.last_name = form.cleaned_data['last_name']
            request.user.email = form.cleaned_data['email']
            request.user.save()

            # Update profile
            form.save()

            messages.success(request, 'Your profile has been updated successfully!')
            return redirect('zhid_auth:profile')
    else:
        form = UserProfileForm(instance=profile, user=request.user)

    return render(request, 'zhid_auth/profile.html', {
        'form': form,
        'profile': profile
    })


def verify_email_view(request, token):
    """Email verification view"""
    try:
        verification_token = get_object_or_404(EmailVerificationToken, token=token)

        if verification_token.is_used:
            messages.error(request, 'This verification link has already been used.')
            return redirect('zhid_auth:login')

        if verification_token.is_expired():
            messages.error(request, 'This verification link has expired. Please request a new one.')
            return redirect('zhid_auth:resend_verification')

        # Mark email as verified
        verification_token.user.profile.is_email_verified = True
        verification_token.user.profile.save()

        # Mark token as used
        verification_token.is_used = True
        verification_token.save()

        messages.success(request, 'Your email has been verified successfully! You can now log in.')
        return redirect('zhid_auth:login')

    except EmailVerificationToken.DoesNotExist:
        messages.error(request, 'Invalid verification link.')
        return redirect('zhid_auth:login')


def resend_verification_view(request):
    """Resend email verification view"""
    if request.method == 'POST':
        form = EmailVerificationForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            user = User.objects.get(email=email)

            # Delete old unused tokens
            EmailVerificationToken.objects.filter(user=user, is_used=False).delete()

            # Create new token
            verification_token = EmailVerificationToken.objects.create(user=user)
            verification_token.send_verification_email()

            messages.success(request, 'Verification email sent! Please check your inbox.')
            return redirect('zhid_auth:login')
    else:
        form = EmailVerificationForm()

    return render(request, 'zhid_auth/verify_email.html', {'form': form})


class CustomPasswordResetView(PasswordResetView):
    """Custom password reset view"""
    form_class = CustomPasswordResetForm
    template_name = 'zhid_auth/password_reset.html'
    email_template_name = 'zhid_auth/email/password_reset.html'
    success_url = reverse_lazy('zhid_auth:password_reset_done')

    def form_valid(self, form):
        messages.success(self.request, 'Password reset email sent! Please check your inbox.')
        return super().form_valid(form)


class CustomPasswordResetDoneView(PasswordResetDoneView):
    """Custom password reset done view"""
    template_name = 'zhid_auth/password_reset_done.html'


class CustomPasswordResetConfirmView(PasswordResetConfirmView):
    """Custom password reset confirm view"""
    template_name = 'zhid_auth/password_reset_confirm.html'
    success_url = reverse_lazy('zhid_auth:password_reset_complete')

    def form_valid(self, form):
        messages.success(self.request, 'Your password has been reset successfully!')
        return super().form_valid(form)


class CustomPasswordResetCompleteView(PasswordResetCompleteView):
    """Custom password reset complete view"""
    template_name = 'zhid_auth/password_reset_complete.html'


class CustomPasswordChangeView(PasswordChangeView):
    """Custom password change view"""
    form_class = CustomPasswordChangeForm
    template_name = 'zhid_auth/password_change.html'
    success_url = reverse_lazy('zhid_auth:password_change_done')

    def form_valid(self, form):
        messages.success(self.request, 'Your password has been changed successfully!')
        return super().form_valid(form)


class CustomPasswordChangeDoneView(PasswordChangeDoneView):
    """Custom password change done view"""
    template_name = 'zhid_auth/password_change_done.html'

