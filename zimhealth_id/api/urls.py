from django.urls import path
from . import views

app_name = 'api'

urlpatterns = [
    # Dashboard URLs
    path('', views.dashboard_index, name='dashboard'),
    path('dashboard/', views.dashboard_index, name='dashboard_index'),
    path('analytics/', views.analytics_dashboard, name='analytics'),
    
    # AJAX endpoints for dashboard
    path('dashboard/stats/', views.dashboard_stats_ajax, name='dashboard_stats_ajax'),
    path('dashboard/activities/', views.dashboard_activities_ajax, name='dashboard_activities_ajax'),
    path('dashboard/system-status/', views.dashboard_system_status_ajax, name='dashboard_system_status_ajax'),
    
    # Patient URLs
    path('patients/', views.patients_list, name='patients'),
    path('patients/new/', views.patient_create, name='patient_create'),
    path('patients/<str:zimhealth_id>/', views.patient_detail, name='patient_detail'),
    path('patients/<str:zimhealth_id>/edit/', views.patient_edit, name='patient_edit'),
    path('patients/<str:zimhealth_id>/qr-code/', views.patient_qr_code, name='patient_qr_code'),
    path('patients/<str:zimhealth_id>/history/', views.patient_medical_history, name='patient_medical_history'),
    path('patients/<str:zimhealth_id>/medical-record/new/', views.medical_record_create, name='medical_record_create_for_patient'),
    path('patients/<str:zimhealth_id>/appointment/new/', views.appointment_create, name='appointment_create_for_patient'),

    # Medical Records URLs
    path('medical-records/', views.medical_records_list, name='medical_records'),
    path('medical-records/new/', views.medical_record_create, name='medical_record_create'),
    path('medical-records/<int:record_id>/', views.medical_record_detail, name='medical_record_detail'),
    path('medical-records/<int:record_id>/edit/', views.medical_record_edit, name='medical_record_edit'),
    path('medical-records/<int:medical_record_id>/prescription/new/', views.prescription_create, name='prescription_create_for_record'),

    # Appointments URLs
    path('appointments/', views.appointments_list, name='appointments'),
    path('appointments/new/', views.appointment_create, name='appointment_create'),
    path('appointments/<int:appointment_id>/', views.appointment_detail, name='appointment_detail'),
    path('appointments/<int:appointment_id>/update-status/', views.appointment_update_status, name='appointment_update_status'),

    # Prescriptions URLs
    path('prescriptions/', views.prescriptions_list, name='prescriptions'),
    path('prescriptions/new/', views.prescription_create, name='prescription_create'),
    path('prescriptions/<int:prescription_id>/', views.prescription_detail, name='prescription_detail'),
    path('prescriptions/<int:prescription_id>/update-status/', views.prescription_update_status, name='prescription_update_status'),

    # AJAX URLs
    path('ajax/search-patients/', views.search_patients_ajax, name='search_patients_ajax'),
]
