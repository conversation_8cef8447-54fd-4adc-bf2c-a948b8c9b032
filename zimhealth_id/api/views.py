from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_POST
from datetime import datetime, timedelta
from .models import Patient, MedicalRecord, Prescription, Appointment
from .forms import PatientForm, MedicalRecordForm, PrescriptionForm, AppointmentForm


@login_required
def dashboard_index(request):
    """Main dashboard view with comprehensive statistics"""
    from django.db.models import Count, Q
    from datetime import datetime, timedelta
    
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)
    
    # Core statistics
    total_patients = Patient.objects.filter(is_active=True).count()
    todays_appointments = Appointment.objects.filter(
        date=today,
        status='scheduled'
    ).count()
    pending_appointments = Appointment.objects.filter(
        status='scheduled',
        date__gte=today
    ).count()
    active_prescriptions = Prescription.objects.filter(status='active').count()
    
    # Additional statistics for template
    total_records = MedicalRecord.objects.count()
    new_records = MedicalRecord.objects.filter(date__gte=week_ago).count()
    expiring_prescriptions = Prescription.objects.filter(
        status='active',
        end_date__lte=today + timedelta(days=7)
    ).count()
    
    # Recent activities - real data from database
    recent_activities = []
    
    # Recent patient registrations
    recent_patients = Patient.objects.filter(
        created_at__gte=timezone.now() - timedelta(hours=24)
    ).order_by('-created_at')[:3]
    
    for patient in recent_patients:
        recent_activities.append({
            'icon': 'user-plus',
            'title': 'New Patient Registered',
            'description': f'{patient.full_name} has been registered in the system',
            'timestamp': patient.created_at
        })
    
    # Recent completed appointments
    recent_completed = Appointment.objects.filter(
        status='completed',
        updated_at__gte=timezone.now() - timedelta(hours=24)
    ).select_related('patient').order_by('-updated_at')[:3]
    
    for appointment in recent_completed:
        recent_activities.append({
            'icon': 'calendar-check',
            'title': 'Appointment Completed',
            'description': f'{appointment.doctor_name} completed consultation with {appointment.patient.full_name}',
            'timestamp': appointment.updated_at
        })
    
    # Recent prescriptions
    recent_prescriptions = Prescription.objects.filter(
        created_at__gte=timezone.now() - timedelta(hours=24)
    ).select_related('medical_record__patient').order_by('-created_at')[:3]
    
    for prescription in recent_prescriptions:
        recent_activities.append({
            'icon': 'pills',
            'title': 'Prescription Added',
            'description': f'New prescription added for patient {prescription.medical_record.patient.zimhealth_id}',
            'timestamp': prescription.created_at
        })
    
    # Sort activities by timestamp (most recent first)
    recent_activities.sort(key=lambda x: x['timestamp'], reverse=True)
    recent_activities = recent_activities[:6]  # Limit to 6 most recent
    
    # Upcoming appointments
    upcoming_appointments = Appointment.objects.filter(
        status='scheduled',
        date__gte=today
    ).select_related('patient').order_by('date', 'time')[:5]

    context = {
        'total_patients': total_patients,
        'todays_appointments': todays_appointments,
        'pending_appointments': pending_appointments,
        'active_prescriptions': active_prescriptions,
        'total_records': total_records,
        'new_records': new_records,
        'expiring_prescriptions': expiring_prescriptions,
        'recent_activities': recent_activities,
        'upcoming_appointments': upcoming_appointments,
    }

    return render(request, 'dashboard/index.html', context)


@login_required
def analytics_dashboard(request):
    """Analytics dashboard view"""
    # Get analytics data
    total_patients = Patient.objects.filter(is_active=True).count()
    monthly_visits = MedicalRecord.objects.filter(
        date__gte=timezone.now() - timedelta(days=30)
    ).count()
    active_prescriptions = Prescription.objects.filter(status='active').count()

    # Get top diagnoses
    top_diagnoses = ['Hypertension', 'Diabetes', 'Malaria', 'Upper Respiratory Infection', 'Gastritis']

    # Get top facilities
    top_facilities = ['Harare Central Hospital', 'Parirenyatwa Hospital', 'Chitungwiza Hospital', 'Mpilo Hospital']

    context = {
        'total_patients': total_patients,
        'monthly_visits': monthly_visits,
        'active_prescriptions': active_prescriptions,
        'top_diagnoses': top_diagnoses,
        'top_facilities': top_facilities,
    }

    return render(request, 'dashboard/analytics.html', context)


@login_required
def patients_list(request):
    """List all patients with search and filtering"""
    patients = Patient.objects.filter(is_active=True).order_by('-created_at')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        patients = patients.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(zimhealth_id__icontains=search_query) |
            Q(national_id__icontains=search_query) |
            Q(phone_number__icontains=search_query)
        )

    # Filtering
    gender_filter = request.GET.get('gender')
    if gender_filter:
        patients = patients.filter(gender=gender_filter)

    blood_type_filter = request.GET.get('blood_type')
    if blood_type_filter:
        patients = patients.filter(blood_type=blood_type_filter)

    # Pagination
    paginator = Paginator(patients, 20)  # Show 20 patients per page
    page_number = request.GET.get('page')
    patients = paginator.get_page(page_number)

    context = {
        'patients': patients,
        'search_query': search_query,
        'gender_filter': gender_filter,
        'blood_type_filter': blood_type_filter,
    }

    return render(request, 'api/patients.html', context)


@login_required
def patient_detail(request, zimhealth_id):
    """Patient detail view"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    # Get recent medical records
    recent_records = patient.medical_records.order_by('-date')[:5]

    # Get upcoming appointments
    upcoming_appointments = patient.appointments.filter(
        status='scheduled',
        date__gte=timezone.now().date()
    ).order_by('date', 'time')[:5]

    # Get active prescriptions count
    active_prescriptions_count = Prescription.objects.filter(
        medical_record__patient=patient,
        status='active'
    ).count()

    context = {
        'patient': patient,
        'recent_records': recent_records,
        'upcoming_appointments': upcoming_appointments,
        'active_prescriptions_count': active_prescriptions_count,
    }

    return render(request, 'api/patient_detail.html', context)


@login_required
def medical_records_list(request):
    """List all medical records"""
    medical_records = MedicalRecord.objects.select_related('patient').order_by('-date')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        medical_records = medical_records.filter(
            Q(patient__first_name__icontains=search_query) |
            Q(patient__last_name__icontains=search_query) |
            Q(diagnosis__icontains=search_query) |
            Q(doctor_name__icontains=search_query) |
            Q(facility_name__icontains=search_query)
        )

    # Date filtering
    from_date = request.GET.get('from_date')
    if from_date:
        medical_records = medical_records.filter(date__gte=from_date)

    to_date = request.GET.get('to_date')
    if to_date:
        medical_records = medical_records.filter(date__lte=to_date)

    # Facility filtering
    facility_filter = request.GET.get('facility')
    if facility_filter:
        medical_records = medical_records.filter(facility_name=facility_filter)

    # Pagination
    paginator = Paginator(medical_records, 10)  # Show 10 records per page
    page_number = request.GET.get('page')
    medical_records = paginator.get_page(page_number)

    context = {
        'medical_records': medical_records,
        'search_query': search_query,
        'from_date': from_date,
        'to_date': to_date,
        'facility_filter': facility_filter,
    }

    return render(request, 'api/medical_records.html', context)


@login_required
def appointments_list(request):
    """List all appointments"""
    appointments = Appointment.objects.select_related('patient').order_by('-date', '-time')

    # Get statistics
    todays_appointments = appointments.filter(date=timezone.now().date()).count()
    pending_appointments = appointments.filter(status='scheduled').count()
    completed_appointments = appointments.filter(status='completed').count()
    cancelled_appointments = appointments.filter(status='cancelled').count()

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        appointments = appointments.filter(
            Q(patient__first_name__icontains=search_query) |
            Q(patient__last_name__icontains=search_query) |
            Q(doctor_name__icontains=search_query) |
            Q(facility_name__icontains=search_query)
        )

    # Status filtering
    status_filter = request.GET.get('status')
    if status_filter:
        appointments = appointments.filter(status=status_filter)

    # Type filtering
    type_filter = request.GET.get('type')
    if type_filter:
        appointments = appointments.filter(appointment_type=type_filter)

    # Date filtering
    date_filter = request.GET.get('date')
    if date_filter:
        appointments = appointments.filter(date=date_filter)

    # Pagination
    paginator = Paginator(appointments, 15)  # Show 15 appointments per page
    page_number = request.GET.get('page')
    appointments = paginator.get_page(page_number)

    context = {
        'appointments': appointments,
        'todays_appointments': todays_appointments,
        'pending_appointments': pending_appointments,
        'completed_appointments': completed_appointments,
        'cancelled_appointments': cancelled_appointments,
        'search_query': search_query,
        'status_filter': status_filter,
        'type_filter': type_filter,
        'date_filter': date_filter,
    }

    return render(request, 'api/appointments.html', context)


@login_required
def prescriptions_list(request):
    """List all prescriptions"""
    prescriptions = Prescription.objects.select_related(
        'medical_record__patient'
    ).order_by('-created_at')

    # Get statistics
    active_prescriptions = prescriptions.filter(status='active').count()
    expiring_prescriptions = prescriptions.filter(
        status='active',
        end_date__lte=timezone.now().date() + timedelta(days=7)
    ).count()
    completed_prescriptions = prescriptions.filter(status='completed').count()
    discontinued_prescriptions = prescriptions.filter(status='discontinued').count()

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        prescriptions = prescriptions.filter(
            Q(medication__icontains=search_query) |
            Q(medical_record__patient__first_name__icontains=search_query) |
            Q(medical_record__patient__last_name__icontains=search_query) |
            Q(medical_record__doctor_name__icontains=search_query)
        )

    # Status filtering
    status_filter = request.GET.get('status')
    if status_filter:
        prescriptions = prescriptions.filter(status=status_filter)

    # Frequency filtering
    frequency_filter = request.GET.get('frequency')
    if frequency_filter:
        prescriptions = prescriptions.filter(frequency=frequency_filter)

    # Date filtering
    start_date = request.GET.get('start_date')
    if start_date:
        prescriptions = prescriptions.filter(start_date__gte=start_date)

    # Pagination
    paginator = Paginator(prescriptions, 10)  # Show 10 prescriptions per page
    page_number = request.GET.get('page')
    prescriptions = paginator.get_page(page_number)

    context = {
        'prescriptions': prescriptions,
        'active_prescriptions': active_prescriptions,
        'expiring_prescriptions': expiring_prescriptions,
        'completed_prescriptions': completed_prescriptions,
        'discontinued_prescriptions': discontinued_prescriptions,
        'search_query': search_query,
        'status_filter': status_filter,
        'frequency_filter': frequency_filter,
        'start_date': start_date,
    }

    return render(request, 'api/prescriptions.html', context)


@login_required
def patient_create(request):
    """Create a new patient"""
    if request.method == 'POST':
        form = PatientForm(request.POST)
        if form.is_valid():
            patient = form.save()
            messages.success(request, f'Patient {patient.full_name} has been successfully registered with ZimHealth ID: {patient.zimhealth_id}')
            return redirect('api:patient_detail', zimhealth_id=patient.zimhealth_id)
    else:
        form = PatientForm()

    context = {
        'form': form,
        'title': 'Add New Patient',
        'common_allergies': ['Penicillin', 'Peanuts', 'Shellfish', 'Latex', 'Dust', 'Pollen', 'Eggs', 'Milk']
    }
    return render(request, 'api/patient_form.html', context)


@login_required
def patient_edit(request, zimhealth_id):
    """Edit an existing patient"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    if request.method == 'POST':
        form = PatientForm(request.POST, instance=patient)
        if form.is_valid():
            patient = form.save()
            messages.success(request, f'Patient {patient.full_name} has been successfully updated.')
            return redirect('api:patient_detail', zimhealth_id=patient.zimhealth_id)
    else:
        form = PatientForm(instance=patient)

    context = {
        'form': form,
        'patient': patient,
        'title': f'Edit Patient - {patient.full_name}',
        'common_allergies': ['Penicillin', 'Peanuts', 'Shellfish', 'Latex', 'Dust', 'Pollen', 'Eggs', 'Milk']
    }
    return render(request, 'api/patient_form.html', context)


@login_required
def medical_record_create(request, zimhealth_id=None):
    """Create a new medical record"""
    patient = None
    if zimhealth_id:
        patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    if request.method == 'POST':
        form = MedicalRecordForm(request.POST)
        if form.is_valid():
            medical_record = form.save(commit=False)
            medical_record.created_by = request.user
            medical_record.save()

            messages.success(request, f'Medical record for {medical_record.patient.full_name} has been created successfully.')

            # Check if user wants to add prescription
            if 'save_and_add_prescription' in request.POST:
                return redirect('api:prescription_create', medical_record_id=medical_record.id)

            return redirect('api:medical_records')
    else:
        form = MedicalRecordForm()
        if patient:
            form.fields['patient'].initial = patient

    context = {
        'form': form,
        'patient': patient,
        'title': 'New Medical Record',
        'patients': Patient.objects.filter(is_active=True).order_by('first_name', 'last_name')
    }
    return render(request, 'api/medical_record_form.html', context)


@login_required
def medical_record_edit(request, record_id):
    """Edit an existing medical record"""
    medical_record = get_object_or_404(MedicalRecord, id=record_id)

    if request.method == 'POST':
        form = MedicalRecordForm(request.POST, instance=medical_record)
        if form.is_valid():
            medical_record = form.save()
            messages.success(request, f'Medical record for {medical_record.patient.full_name} has been updated successfully.')
            return redirect('api:medical_records')
    else:
        form = MedicalRecordForm(instance=medical_record)

    context = {
        'form': form,
        'record': medical_record,
        'title': f'Edit Medical Record - {medical_record.patient.full_name}',
        'patients': Patient.objects.filter(is_active=True).order_by('first_name', 'last_name')
    }
    return render(request, 'api/medical_record_form.html', context)


@login_required
def prescription_create(request, medical_record_id=None):
    """Create a new prescription"""
    medical_record = None
    if medical_record_id:
        medical_record = get_object_or_404(MedicalRecord, id=medical_record_id)

    if request.method == 'POST':
        form = PrescriptionForm(request.POST)
        if form.is_valid():
            prescription = form.save(commit=False)
            prescription.prescribed_by = request.user
            prescription.save()

            messages.success(request, f'Prescription for {prescription.patient.full_name} has been created successfully.')
            return redirect('api:prescriptions')
    else:
        form = PrescriptionForm()
        if medical_record:
            form.fields['medical_record'].initial = medical_record

    context = {
        'form': form,
        'medical_record': medical_record,
        'title': 'New Prescription',
        'medical_records': MedicalRecord.objects.select_related('patient').order_by('-date')[:50]
    }
    return render(request, 'api/prescription_form.html', context)


@login_required
def appointment_create(request, zimhealth_id=None):
    """Create a new appointment"""
    patient = None
    if zimhealth_id:
        patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    if request.method == 'POST':
        form = AppointmentForm(request.POST)
        if form.is_valid():
            appointment = form.save(commit=False)
            appointment.created_by = request.user
            appointment.save()

            messages.success(request, f'Appointment for {appointment.patient.full_name} has been scheduled successfully.')
            return redirect('api:appointments')
    else:
        form = AppointmentForm()
        if patient:
            form.fields['patient'].initial = patient

    context = {
        'form': form,
        'patient': patient,
        'title': 'Schedule New Appointment',
        'patients': Patient.objects.filter(is_active=True).order_by('first_name', 'last_name')
    }
    return render(request, 'api/appointment_form.html', context)


@login_required
@require_POST
def appointment_update_status(request, appointment_id):
    """Update appointment status via AJAX"""
    appointment = get_object_or_404(Appointment, id=appointment_id)
    new_status = request.POST.get('status')

    if new_status in ['completed', 'cancelled', 'no_show']:
        appointment.status = new_status
        if new_status == 'cancelled':
            appointment.cancelled_at = timezone.now()
            appointment.cancelled_by = request.user
            appointment.cancellation_reason = request.POST.get('reason', '')
        appointment.save()

        messages.success(request, f'Appointment status updated to {new_status}.')
        return JsonResponse({'success': True, 'status': new_status})

    return JsonResponse({'success': False, 'error': 'Invalid status'})


@login_required
@require_POST
def prescription_update_status(request, prescription_id):
    """Update prescription status via AJAX"""
    prescription = get_object_or_404(Prescription, id=prescription_id)
    new_status = request.POST.get('status')

    if new_status in ['active', 'completed', 'discontinued', 'on_hold']:
        prescription.status = new_status
        prescription.save()

        messages.success(request, f'Prescription status updated to {new_status}.')
        return JsonResponse({'success': True, 'status': new_status})

    return JsonResponse({'success': False, 'error': 'Invalid status'})


@login_required
def patient_qr_code(request, zimhealth_id):
    """Display patient QR code"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    context = {
        'patient': patient,
    }
    return render(request, 'api/patient_qr_code.html', context)


@login_required
def search_patients_ajax(request):
    """AJAX endpoint for patient search"""
    query = request.GET.get('q', '')
    patients = []

    if query and len(query) >= 2:
        patient_results = Patient.objects.filter(
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(zimhealth_id__icontains=query) |
            Q(national_id__icontains=query)
        ).filter(is_active=True)[:10]

        patients = [
            {
                'zimhealth_id': p.zimhealth_id,
                'full_name': p.full_name,
                'national_id': p.national_id or '',
                'phone_number': p.phone_number,
                'age': p.age
            }
            for p in patient_results
        ]

    return JsonResponse({'patients': patients})


@login_required
def medical_record_detail(request, record_id):
    """Detailed view of a medical record"""
    medical_record = get_object_or_404(MedicalRecord, id=record_id)
    prescriptions = medical_record.prescriptions.all()

    context = {
        'medical_record': medical_record,
        'prescriptions': prescriptions,
    }
    return render(request, 'api/medical_record_detail.html', context)


@login_required
def appointment_detail(request, appointment_id):
    """Detailed view of an appointment"""
    appointment = get_object_or_404(Appointment, id=appointment_id)

    context = {
        'appointment': appointment,
    }
    return render(request, 'api/appointment_detail.html', context)


@login_required
def prescription_detail(request, prescription_id):
    """Detailed view of a prescription"""
    prescription = get_object_or_404(Prescription, id=prescription_id)

    context = {
        'prescription': prescription,
    }
    return render(request, 'api/prescription_detail.html', context)


@login_required
def patient_medical_history(request, zimhealth_id):
    """Complete medical history for a patient"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    # Get all medical records
    medical_records = patient.medical_records.order_by('-date')

    # Get all prescriptions
    prescriptions = Prescription.objects.filter(
        medical_record__patient=patient
    ).order_by('-created_at')

    # Get all appointments
    appointments = patient.appointments.order_by('-date', '-time')

    # Pagination for medical records
    paginator = Paginator(medical_records, 10)
    page_number = request.GET.get('page')
    medical_records = paginator.get_page(page_number)

    context = {
        'patient': patient,
        'medical_records': medical_records,
        'prescriptions': prescriptions[:10],  # Latest 10 prescriptions
        'appointments': appointments[:10],    # Latest 10 appointments
        'total_records': patient.medical_records.count(),
        'total_prescriptions': prescriptions.count(),
        'total_appointments': appointments.count(),
    }
    return render(request, 'api/patient_medical_history.html', context)


@login_required
def dashboard_stats_ajax(request):
    """Enhanced AJAX endpoint for real-time dashboard statistics"""
    from django.db.models import Count, Q
    
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    
    # Calculate comprehensive statistics
    stats = {
        'total_patients': Patient.objects.filter(is_active=True).count(),
        'todays_appointments': Appointment.objects.filter(
            date=today, status='scheduled'
        ).count(),
        'pending_appointments': Appointment.objects.filter(
            status='scheduled', date__gte=today
        ).count(),
        'active_prescriptions': Prescription.objects.filter(status='active').count(),
        'total_records': MedicalRecord.objects.count(),
        'new_records_today': MedicalRecord.objects.filter(date=today).count(),
        'new_records_week': MedicalRecord.objects.filter(date__gte=week_ago).count(),
        'expiring_prescriptions': Prescription.objects.filter(
            status='active',
            end_date__lte=today + timedelta(days=7)
        ).count(),
        'completed_appointments_today': Appointment.objects.filter(
            date=today, status='completed'
        ).count(),
        'cancelled_appointments_today': Appointment.objects.filter(
            date=today, status='cancelled'
        ).count(),
        # Growth metrics
        'patients_growth': _calculate_patient_growth(),
        'appointments_growth': _calculate_appointments_growth(),
        'records_growth': _calculate_records_growth(),
    }
    
    return JsonResponse(stats)

def _calculate_patient_growth():
    """Calculate patient growth percentage"""
    today = timezone.now().date()
    this_month = Patient.objects.filter(
        created_at__month=today.month,
        created_at__year=today.year,
        is_active=True
    ).count()
    
    last_month = today.replace(day=1) - timedelta(days=1)
    last_month_count = Patient.objects.filter(
        created_at__month=last_month.month,
        created_at__year=last_month.year,
        is_active=True
    ).count()
    
    if last_month_count == 0:
        return 100 if this_month > 0 else 0
    
    return round(((this_month - last_month_count) / last_month_count) * 100, 1)

def _calculate_appointments_growth():
    """Calculate appointments growth percentage"""
    today = timezone.now().date()
    this_week_start = today - timedelta(days=today.weekday())
    last_week_start = this_week_start - timedelta(days=7)
    
    this_week = Appointment.objects.filter(
        date__gte=this_week_start,
        date__lt=this_week_start + timedelta(days=7)
    ).count()
    
    last_week = Appointment.objects.filter(
        date__gte=last_week_start,
        date__lt=this_week_start
    ).count()
    
    if last_week == 0:
        return 100 if this_week > 0 else 0
    
    return round(((this_week - last_week) / last_week) * 100, 1)

def _calculate_records_growth():
    """Calculate medical records growth percentage"""
    today = timezone.now().date()
    this_week_start = today - timedelta(days=today.weekday())
    last_week_start = this_week_start - timedelta(days=7)
    
    this_week = MedicalRecord.objects.filter(
        date__gte=this_week_start,
        date__lt=this_week_start + timedelta(days=7)
    ).count()
    
    last_week = MedicalRecord.objects.filter(
        date__gte=last_week_start,
        date__lt=this_week_start
    ).count()
    
    if last_week == 0:
        return 100 if this_week > 0 else 0
    
    return round(((this_week - last_week) / last_week) * 100, 1)


@login_required
def dashboard_activities_ajax(request):
    """AJAX endpoint for recent activities"""
    activities = []
    
    # Recent patient registrations (last 24 hours)
    recent_patients = Patient.objects.filter(
        created_at__gte=timezone.now() - timedelta(hours=24)
    ).order_by('-created_at')[:5]
    
    for patient in recent_patients:
        activities.append({
            'icon': 'user-plus',
            'title': 'New Patient Registered',
            'description': f'{patient.full_name} has been registered in the system',
            'timestamp': patient.created_at.isoformat(),
            'type': 'patient'
        })
    
    # Recent completed appointments
    recent_completed = Appointment.objects.filter(
        status='completed',
        updated_at__gte=timezone.now() - timedelta(hours=24)
    ).select_related('patient').order_by('-updated_at')[:5]
    
    for appointment in recent_completed:
        activities.append({
            'icon': 'calendar-check',
            'title': 'Appointment Completed',
            'description': f'{appointment.doctor_name} completed consultation with {appointment.patient.full_name}',
            'timestamp': appointment.updated_at.isoformat(),
            'type': 'appointment'
        })
    
    # Recent prescriptions
    recent_prescriptions = Prescription.objects.filter(
        created_at__gte=timezone.now() - timedelta(hours=24)
    ).select_related('medical_record__patient').order_by('-created_at')[:5]
    
    for prescription in recent_prescriptions:
        activities.append({
            'icon': 'pills',
            'title': 'Prescription Added',
            'description': f'New prescription ({prescription.medication}) added for {prescription.medical_record.patient.full_name}',
            'timestamp': prescription.created_at.isoformat(),
            'type': 'prescription'
        })
    
    # Recent medical records
    recent_records = MedicalRecord.objects.filter(
        created_at__gte=timezone.now() - timedelta(hours=24)
    ).select_related('patient').order_by('-created_at')[:3]
    
    for record in recent_records:
        activities.append({
            'icon': 'file-medical',
            'title': 'Medical Record Added',
            'description': f'New medical record for {record.patient.full_name} - {record.diagnosis}',
            'timestamp': record.created_at.isoformat(),
            'type': 'record'
        })
    
    # Sort by timestamp and limit
    activities.sort(key=lambda x: x['timestamp'], reverse=True)
    activities = activities[:10]
    
    return JsonResponse({'activities': activities})
