from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_POST, require_GET
from django.views.decorators.csrf import csrf_exempt
from django.core.exceptions import ValidationError
from datetime import datetime, timedelta
import json
from .models import Patient, MedicalRecord, Prescription, Appointment
from .forms import PatientForm, MedicalRecordForm, PrescriptionForm, AppointmentForm


@login_required
def dashboard_index(request):
    """Enhanced dashboard command center view"""
    today = timezone.now().date()
    now = timezone.now()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    # Core Statistics
    total_patients = Patient.objects.filter(is_active=True).count()
    todays_appointments = Appointment.objects.filter(date=today, status='scheduled').count()
    pending_appointments = Appointment.objects.filter(status='scheduled', date__gte=today).count()
    active_prescriptions = Prescription.objects.filter(status='active').count()
    total_records = MedicalRecord.objects.count()

    # Advanced Analytics
    new_patients_today = Patient.objects.filter(created_at__date=today).count()
    new_patients_week = Patient.objects.filter(created_at__gte=now - timedelta(days=7)).count()
    completed_appointments_today = Appointment.objects.filter(date=today, status='completed').count()
    cancelled_appointments_today = Appointment.objects.filter(date=today, status='cancelled').count()

    # Critical Alerts
    overdue_appointments = Appointment.objects.filter(
        date__lt=today, status='scheduled'
    ).count()
    expiring_prescriptions = Prescription.objects.filter(
        status='active',
        end_date__lte=today + timedelta(days=7),
        end_date__isnull=False
    ).count()
    patients_no_recent_visit = Patient.objects.filter(
        is_active=True,
        last_visit__lt=today - timedelta(days=365)
    ).count() if Patient.objects.filter(last_visit__isnull=False).exists() else 0

    # System Performance Metrics
    avg_appointments_per_day = Appointment.objects.filter(
        created_at__gte=now - timedelta(days=30)
    ).count() / 30

    # Recent Real Activities
    recent_patients = Patient.objects.filter(
        created_at__gte=now - timedelta(hours=24)
    ).order_by('-created_at')[:3]

    recent_appointments = Appointment.objects.filter(
        updated_at__gte=now - timedelta(hours=24)
    ).select_related('patient').order_by('-updated_at')[:3]

    recent_records = MedicalRecord.objects.filter(
        created_at__gte=now - timedelta(hours=24)
    ).select_related('patient').order_by('-created_at')[:3]

    # Build activity timeline
    recent_activities = []

    for patient in recent_patients:
        recent_activities.append({
            'icon': 'user-plus',
            'title': 'New Patient Registered',
            'description': f'{patient.full_name} has been registered in the system',
            'timestamp': patient.created_at,
            'type': 'patient',
            'url': f'/api/patients/{patient.zimhealth_id}/',
            'priority': 'normal'
        })

    for appointment in recent_appointments:
        status_icons = {
            'scheduled': 'calendar-plus',
            'completed': 'calendar-check',
            'cancelled': 'calendar-times',
            'no_show': 'user-times'
        }
        recent_activities.append({
            'icon': status_icons.get(appointment.status, 'calendar'),
            'title': f'Appointment {appointment.status.title()}',
            'description': f'{appointment.patient.full_name} - {appointment.doctor_name}',
            'timestamp': appointment.updated_at,
            'type': 'appointment',
            'url': f'/api/appointments/{appointment.id}/',
            'priority': 'high' if appointment.status == 'cancelled' else 'normal'
        })

    for record in recent_records:
        recent_activities.append({
            'icon': 'file-medical-alt',
            'title': 'Medical Record Created',
            'description': f'{record.patient.full_name} - {record.diagnosis[:50]}...',
            'timestamp': record.created_at,
            'type': 'record',
            'url': f'/api/medical-records/{record.id}/',
            'priority': 'normal'
        })

    # Sort activities by timestamp
    recent_activities.sort(key=lambda x: x['timestamp'], reverse=True)
    recent_activities = recent_activities[:10]

    # Get upcoming appointments
    upcoming_appointments = Appointment.objects.filter(
        status='scheduled',
        date__gte=timezone.now().date()
    ).order_by('date', 'time')[:5]

    context = {
        'total_patients': total_patients,
        'todays_appointments': todays_appointments,
        'pending_appointments': pending_appointments,
        'active_prescriptions': active_prescriptions,
        'recent_activities': recent_activities,
        'upcoming_appointments': upcoming_appointments,
    }

    return render(request, 'dashboard/index.html', context)


@login_required
def analytics_dashboard(request):
    """Analytics dashboard view"""
    # Get analytics data
    total_patients = Patient.objects.filter(is_active=True).count()
    monthly_visits = MedicalRecord.objects.filter(
        date__gte=timezone.now() - timedelta(days=30)
    ).count()
    active_prescriptions = Prescription.objects.filter(status='active').count()

    # Get top diagnoses
    top_diagnoses = ['Hypertension', 'Diabetes', 'Malaria', 'Upper Respiratory Infection', 'Gastritis']

    # Get top facilities
    top_facilities = ['Harare Central Hospital', 'Parirenyatwa Hospital', 'Chitungwiza Hospital', 'Mpilo Hospital']

    context = {
        'total_patients': total_patients,
        'monthly_visits': monthly_visits,
        'active_prescriptions': active_prescriptions,
        'top_diagnoses': top_diagnoses,
        'top_facilities': top_facilities,
    }

    return render(request, 'dashboard/analytics.html', context)


@login_required
def patients_list(request):
    """List all patients with search and filtering"""
    patients = Patient.objects.filter(is_active=True).order_by('-created_at')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        patients = patients.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(zimhealth_id__icontains=search_query) |
            Q(national_id__icontains=search_query) |
            Q(phone_number__icontains=search_query)
        )

    # Filtering
    gender_filter = request.GET.get('gender')
    if gender_filter:
        patients = patients.filter(gender=gender_filter)

    blood_type_filter = request.GET.get('blood_type')
    if blood_type_filter:
        patients = patients.filter(blood_type=blood_type_filter)

    # Pagination
    paginator = Paginator(patients, 20)  # Show 20 patients per page
    page_number = request.GET.get('page')
    patients = paginator.get_page(page_number)

    context = {
        'patients': patients,
        'search_query': search_query,
        'gender_filter': gender_filter,
        'blood_type_filter': blood_type_filter,
    }

    return render(request, 'api/patients.html', context)


@login_required
def patient_detail(request, zimhealth_id):
    """Patient detail view"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    # Get recent medical records
    recent_records = patient.medical_records.order_by('-date')[:5]

    # Get upcoming appointments
    upcoming_appointments = patient.appointments.filter(
        status='scheduled',
        date__gte=timezone.now().date()
    ).order_by('date', 'time')[:5]

    # Get active prescriptions count
    active_prescriptions_count = Prescription.objects.filter(
        medical_record__patient=patient,
        status='active'
    ).count()

    context = {
        'patient': patient,
        'recent_records': recent_records,
        'upcoming_appointments': upcoming_appointments,
        'active_prescriptions_count': active_prescriptions_count,
    }

    return render(request, 'api/patient_detail.html', context)


@login_required
def medical_records_list(request):
    """List all medical records"""
    medical_records = MedicalRecord.objects.select_related('patient').order_by('-date')

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        medical_records = medical_records.filter(
            Q(patient__first_name__icontains=search_query) |
            Q(patient__last_name__icontains=search_query) |
            Q(diagnosis__icontains=search_query) |
            Q(doctor_name__icontains=search_query) |
            Q(facility_name__icontains=search_query)
        )

    # Date filtering
    from_date = request.GET.get('from_date')
    if from_date:
        medical_records = medical_records.filter(date__gte=from_date)

    to_date = request.GET.get('to_date')
    if to_date:
        medical_records = medical_records.filter(date__lte=to_date)

    # Facility filtering
    facility_filter = request.GET.get('facility')
    if facility_filter:
        medical_records = medical_records.filter(facility_name=facility_filter)

    # Pagination
    paginator = Paginator(medical_records, 10)  # Show 10 records per page
    page_number = request.GET.get('page')
    medical_records = paginator.get_page(page_number)

    context = {
        'medical_records': medical_records,
        'search_query': search_query,
        'from_date': from_date,
        'to_date': to_date,
        'facility_filter': facility_filter,
    }

    return render(request, 'api/medical_records.html', context)


@login_required
def appointments_list(request):
    """List all appointments"""
    appointments = Appointment.objects.select_related('patient').order_by('-date', '-time')

    # Get statistics
    todays_appointments = appointments.filter(date=timezone.now().date()).count()
    pending_appointments = appointments.filter(status='scheduled').count()
    completed_appointments = appointments.filter(status='completed').count()
    cancelled_appointments = appointments.filter(status='cancelled').count()

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        appointments = appointments.filter(
            Q(patient__first_name__icontains=search_query) |
            Q(patient__last_name__icontains=search_query) |
            Q(doctor_name__icontains=search_query) |
            Q(facility_name__icontains=search_query)
        )

    # Status filtering
    status_filter = request.GET.get('status')
    if status_filter:
        appointments = appointments.filter(status=status_filter)

    # Type filtering
    type_filter = request.GET.get('type')
    if type_filter:
        appointments = appointments.filter(appointment_type=type_filter)

    # Date filtering
    date_filter = request.GET.get('date')
    if date_filter:
        appointments = appointments.filter(date=date_filter)

    # Pagination
    paginator = Paginator(appointments, 15)  # Show 15 appointments per page
    page_number = request.GET.get('page')
    appointments = paginator.get_page(page_number)

    context = {
        'appointments': appointments,
        'todays_appointments': todays_appointments,
        'pending_appointments': pending_appointments,
        'completed_appointments': completed_appointments,
        'cancelled_appointments': cancelled_appointments,
        'search_query': search_query,
        'status_filter': status_filter,
        'type_filter': type_filter,
        'date_filter': date_filter,
    }

    return render(request, 'api/appointments.html', context)


@login_required
def prescriptions_list(request):
    """List all prescriptions"""
    prescriptions = Prescription.objects.select_related(
        'medical_record__patient'
    ).order_by('-created_at')

    # Get statistics
    active_prescriptions = prescriptions.filter(status='active').count()
    expiring_prescriptions = prescriptions.filter(
        status='active',
        end_date__lte=timezone.now().date() + timedelta(days=7)
    ).count()
    completed_prescriptions = prescriptions.filter(status='completed').count()
    discontinued_prescriptions = prescriptions.filter(status='discontinued').count()

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        prescriptions = prescriptions.filter(
            Q(medication__icontains=search_query) |
            Q(medical_record__patient__first_name__icontains=search_query) |
            Q(medical_record__patient__last_name__icontains=search_query) |
            Q(medical_record__doctor_name__icontains=search_query)
        )

    # Status filtering
    status_filter = request.GET.get('status')
    if status_filter:
        prescriptions = prescriptions.filter(status=status_filter)

    # Frequency filtering
    frequency_filter = request.GET.get('frequency')
    if frequency_filter:
        prescriptions = prescriptions.filter(frequency=frequency_filter)

    # Date filtering
    start_date = request.GET.get('start_date')
    if start_date:
        prescriptions = prescriptions.filter(start_date__gte=start_date)

    # Pagination
    paginator = Paginator(prescriptions, 10)  # Show 10 prescriptions per page
    page_number = request.GET.get('page')
    prescriptions = paginator.get_page(page_number)

    context = {
        'prescriptions': prescriptions,
        'active_prescriptions': active_prescriptions,
        'expiring_prescriptions': expiring_prescriptions,
        'completed_prescriptions': completed_prescriptions,
        'discontinued_prescriptions': discontinued_prescriptions,
        'search_query': search_query,
        'status_filter': status_filter,
        'frequency_filter': frequency_filter,
        'start_date': start_date,
    }

    return render(request, 'api/prescriptions.html', context)


@login_required
def patient_create(request):
    """Create a new patient"""
    if request.method == 'POST':
        form = PatientForm(request.POST)
        if form.is_valid():
            patient = form.save()
            messages.success(request, f'Patient {patient.full_name} has been successfully registered with ZimHealth ID: {patient.zimhealth_id}')
            return redirect('api:patient_detail', zimhealth_id=patient.zimhealth_id)
    else:
        form = PatientForm()

    context = {
        'form': form,
        'title': 'Add New Patient',
        'common_allergies': ['Penicillin', 'Peanuts', 'Shellfish', 'Latex', 'Dust', 'Pollen', 'Eggs', 'Milk']
    }
    return render(request, 'api/patient_form.html', context)


@login_required
def patient_edit(request, zimhealth_id):
    """Edit an existing patient"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    if request.method == 'POST':
        form = PatientForm(request.POST, instance=patient)
        if form.is_valid():
            patient = form.save()
            messages.success(request, f'Patient {patient.full_name} has been successfully updated.')
            return redirect('api:patient_detail', zimhealth_id=patient.zimhealth_id)
    else:
        form = PatientForm(instance=patient)

    context = {
        'form': form,
        'patient': patient,
        'title': f'Edit Patient - {patient.full_name}',
        'common_allergies': ['Penicillin', 'Peanuts', 'Shellfish', 'Latex', 'Dust', 'Pollen', 'Eggs', 'Milk']
    }
    return render(request, 'api/patient_form.html', context)


@login_required
def medical_record_create(request, zimhealth_id=None):
    """Create a new medical record"""
    patient = None
    if zimhealth_id:
        patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    if request.method == 'POST':
        form = MedicalRecordForm(request.POST)
        if form.is_valid():
            medical_record = form.save(commit=False)
            medical_record.created_by = request.user
            medical_record.save()

            messages.success(request, f'Medical record for {medical_record.patient.full_name} has been created successfully.')

            # Check if user wants to add prescription
            if 'save_and_add_prescription' in request.POST:
                return redirect('api:prescription_create', medical_record_id=medical_record.id)

            return redirect('api:medical_records')
    else:
        form = MedicalRecordForm()
        if patient:
            form.fields['patient'].initial = patient

    context = {
        'form': form,
        'patient': patient,
        'title': 'New Medical Record',
        'patients': Patient.objects.filter(is_active=True).order_by('first_name', 'last_name')
    }
    return render(request, 'api/medical_record_form.html', context)


@login_required
def medical_record_edit(request, record_id):
    """Edit an existing medical record"""
    medical_record = get_object_or_404(MedicalRecord, id=record_id)

    if request.method == 'POST':
        form = MedicalRecordForm(request.POST, instance=medical_record)
        if form.is_valid():
            medical_record = form.save()
            messages.success(request, f'Medical record for {medical_record.patient.full_name} has been updated successfully.')
            return redirect('api:medical_records')
    else:
        form = MedicalRecordForm(instance=medical_record)

    context = {
        'form': form,
        'record': medical_record,
        'title': f'Edit Medical Record - {medical_record.patient.full_name}',
        'patients': Patient.objects.filter(is_active=True).order_by('first_name', 'last_name')
    }
    return render(request, 'api/medical_record_form.html', context)


@login_required
def prescription_create(request, medical_record_id=None):
    """Create a new prescription"""
    medical_record = None
    if medical_record_id:
        medical_record = get_object_or_404(MedicalRecord, id=medical_record_id)

    if request.method == 'POST':
        form = PrescriptionForm(request.POST)
        if form.is_valid():
            prescription = form.save(commit=False)
            prescription.prescribed_by = request.user
            prescription.save()

            messages.success(request, f'Prescription for {prescription.patient.full_name} has been created successfully.')
            return redirect('api:prescriptions')
    else:
        form = PrescriptionForm()
        if medical_record:
            form.fields['medical_record'].initial = medical_record

    context = {
        'form': form,
        'medical_record': medical_record,
        'title': 'New Prescription',
        'medical_records': MedicalRecord.objects.select_related('patient').order_by('-date')[:50]
    }
    return render(request, 'api/prescription_form.html', context)


@login_required
def appointment_create(request, zimhealth_id=None):
    """Create a new appointment"""
    patient = None
    if zimhealth_id:
        patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    if request.method == 'POST':
        form = AppointmentForm(request.POST)
        if form.is_valid():
            appointment = form.save(commit=False)
            appointment.created_by = request.user
            appointment.save()

            messages.success(request, f'Appointment for {appointment.patient.full_name} has been scheduled successfully.')
            return redirect('api:appointments')
    else:
        form = AppointmentForm()
        if patient:
            form.fields['patient'].initial = patient

    context = {
        'form': form,
        'patient': patient,
        'title': 'Schedule New Appointment',
        'patients': Patient.objects.filter(is_active=True).order_by('first_name', 'last_name')
    }
    return render(request, 'api/appointment_form.html', context)


@login_required
@require_POST
def appointment_update_status(request, appointment_id):
    """Update appointment status via AJAX"""
    appointment = get_object_or_404(Appointment, id=appointment_id)
    new_status = request.POST.get('status')

    if new_status in ['completed', 'cancelled', 'no_show']:
        appointment.status = new_status
        if new_status == 'cancelled':
            appointment.cancelled_at = timezone.now()
            appointment.cancelled_by = request.user
            appointment.cancellation_reason = request.POST.get('reason', '')
        appointment.save()

        messages.success(request, f'Appointment status updated to {new_status}.')
        return JsonResponse({'success': True, 'status': new_status})

    return JsonResponse({'success': False, 'error': 'Invalid status'})


@login_required
@require_POST
def prescription_update_status(request, prescription_id):
    """Update prescription status via AJAX"""
    prescription = get_object_or_404(Prescription, id=prescription_id)
    new_status = request.POST.get('status')

    if new_status in ['active', 'completed', 'discontinued', 'on_hold']:
        prescription.status = new_status
        prescription.save()

        messages.success(request, f'Prescription status updated to {new_status}.')
        return JsonResponse({'success': True, 'status': new_status})

    return JsonResponse({'success': False, 'error': 'Invalid status'})


@login_required
def patient_qr_code(request, zimhealth_id):
    """Display patient QR code"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    context = {
        'patient': patient,
    }
    return render(request, 'api/patient_qr_code.html', context)


@login_required
def search_patients_ajax(request):
    """AJAX endpoint for patient search"""
    query = request.GET.get('q', '')
    patients = []

    if query and len(query) >= 2:
        patient_results = Patient.objects.filter(
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(zimhealth_id__icontains=query) |
            Q(national_id__icontains=query)
        ).filter(is_active=True)[:10]

        patients = [
            {
                'zimhealth_id': p.zimhealth_id,
                'full_name': p.full_name,
                'national_id': p.national_id or '',
                'phone_number': p.phone_number,
                'age': p.age
            }
            for p in patient_results
        ]

    return JsonResponse({'patients': patients})


@login_required
def medical_record_detail(request, record_id):
    """Detailed view of a medical record"""
    medical_record = get_object_or_404(MedicalRecord, id=record_id)
    prescriptions = medical_record.prescriptions.all()

    context = {
        'medical_record': medical_record,
        'prescriptions': prescriptions,
    }
    return render(request, 'api/medical_record_detail.html', context)


@login_required
def appointment_detail(request, appointment_id):
    """Detailed view of an appointment"""
    appointment = get_object_or_404(Appointment, id=appointment_id)

    context = {
        'appointment': appointment,
    }
    return render(request, 'api/appointment_detail.html', context)


@login_required
def prescription_detail(request, prescription_id):
    """Detailed view of a prescription"""
    prescription = get_object_or_404(Prescription, id=prescription_id)

    context = {
        'prescription': prescription,
    }
    return render(request, 'api/prescription_detail.html', context)


@login_required
def patient_medical_history(request, zimhealth_id):
    """Complete medical history for a patient"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id)

    # Get all medical records
    medical_records = patient.medical_records.order_by('-date')

    # Get all prescriptions
    prescriptions = Prescription.objects.filter(
        medical_record__patient=patient
    ).order_by('-created_at')

    # Get all appointments
    appointments = patient.appointments.order_by('-date', '-time')

    # Pagination for medical records
    paginator = Paginator(medical_records, 10)
    page_number = request.GET.get('page')
    medical_records = paginator.get_page(page_number)

    context = {
        'patient': patient,
        'medical_records': medical_records,
        'prescriptions': prescriptions[:10],  # Latest 10 prescriptions
        'appointments': appointments[:10],    # Latest 10 appointments
        'total_records': patient.medical_records.count(),
        'total_prescriptions': prescriptions.count(),
        'total_appointments': appointments.count(),
    }
    return render(request, 'api/patient_medical_history.html', context)


@login_required
def dashboard_stats_ajax(request):
    """AJAX endpoint for dashboard statistics"""
    today = timezone.now().date()

    stats = {
        'total_patients': Patient.objects.filter(is_active=True).count(),
        'todays_appointments': Appointment.objects.filter(
            date=today, status='scheduled'
        ).count(),
        'pending_appointments': Appointment.objects.filter(
            status='scheduled', date__gte=today
        ).count(),
        'active_prescriptions': Prescription.objects.filter(status='active').count(),
        'new_records_today': MedicalRecord.objects.filter(date__date=today).count(),
        'expiring_prescriptions': Prescription.objects.filter(
            status='active',
            end_date__lte=today + timedelta(days=7)
        ).count(),
    }

    return JsonResponse(stats)


@login_required
def dashboard_activities_ajax(request):
    """AJAX endpoint for recent activities"""
    activities = []

    # Recent patient registrations (last 24 hours)
    recent_patients = Patient.objects.filter(
        created_at__gte=timezone.now() - timedelta(hours=24)
    ).order_by('-created_at')[:5]

    for patient in recent_patients:
        activities.append({
            'icon': 'user-plus',
            'title': 'New Patient Registered',
            'description': f'{patient.full_name} has been registered in the system',
            'timestamp': patient.created_at.isoformat(),
            'type': 'patient'
        })

    # Recent completed appointments
    recent_completed = Appointment.objects.filter(
        status='completed',
        updated_at__gte=timezone.now() - timedelta(hours=24)
    ).select_related('patient').order_by('-updated_at')[:5]

    for appointment in recent_completed:
        activities.append({
            'icon': 'calendar-check',
            'title': 'Appointment Completed',
            'description': f'{appointment.doctor_name} completed consultation with {appointment.patient.full_name}',
            'timestamp': appointment.updated_at.isoformat(),
            'type': 'appointment'
        })

    # Recent medical records
    recent_records = MedicalRecord.objects.filter(
        created_at__gte=timezone.now() - timedelta(hours=24)
    ).select_related('patient').order_by('-created_at')[:5]

    for record in recent_records:
        activities.append({
            'icon': 'file-medical-alt',
            'title': 'Medical Record Created',
            'description': f'New medical record added for {record.patient.full_name}',
            'timestamp': record.created_at.isoformat(),
            'type': 'record'
        })

    # Recent prescriptions
    recent_prescriptions = Prescription.objects.filter(
        created_at__gte=timezone.now() - timedelta(hours=24)
    ).select_related('medical_record__patient').order_by('-created_at')[:5]

    for prescription in recent_prescriptions:
        activities.append({
            'icon': 'pills',
            'title': 'Prescription Issued',
            'description': f'{prescription.medication} prescribed for {prescription.patient.full_name}',
            'timestamp': prescription.created_at.isoformat(),
            'type': 'prescription'
        })

    # Sort activities by timestamp (most recent first)
    activities.sort(key=lambda x: x['timestamp'], reverse=True)

    return JsonResponse({'activities': activities[:10]})


@login_required
def dashboard_system_status_ajax(request):
    """AJAX endpoint for system status"""
    today = timezone.now().date()

    status = {
        'system_health': 'healthy',
        'database_status': 'connected',
        'last_backup': (timezone.now() - timedelta(hours=6)).isoformat(),
        'active_users': 1,  # Could be enhanced with session tracking
        'server_uptime': '99.9%',
        'response_time': '120ms'
    }

    return JsonResponse(status)


@login_required
def quick_patient_search_ajax(request):
    """AJAX endpoint for quick patient search (referenced in dashboard.js)"""
    query = request.GET.get('q', '')
    patients = []

    if query and len(query) >= 2:
        patient_results = Patient.objects.filter(
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(zimhealth_id__icontains=query) |
            Q(national_id__icontains=query) |
            Q(phone_number__icontains=query)
        ).filter(is_active=True)[:8]

        patients = [
            {
                'zimhealth_id': p.zimhealth_id,
                'full_name': p.full_name,
                'national_id': p.national_id or '',
                'phone_number': p.phone_number,
                'age': p.age,
                'gender': p.get_gender_display(),
                'blood_type': p.blood_type or 'Unknown',
                'last_visit': p.last_visit.strftime('%Y-%m-%d') if p.last_visit else 'Never',
                'url': f'/api/patients/{p.zimhealth_id}/'
            }
            for p in patient_results
        ]

    return JsonResponse({'patients': patients})


@login_required
def patient_autocomplete_ajax(request):
    """AJAX endpoint for patient autocomplete"""
    query = request.GET.get('term', '')
    suggestions = []

    if query and len(query) >= 2:
        patients = Patient.objects.filter(
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(zimhealth_id__icontains=query)
        ).filter(is_active=True)[:10]

        suggestions = [
            {
                'id': p.zimhealth_id,
                'label': f'{p.full_name} ({p.zimhealth_id})',
                'value': p.full_name
            }
            for p in patients
        ]

    return JsonResponse(suggestions, safe=False)


@login_required
def patients_filter_ajax(request):
    """AJAX endpoint for advanced patient filtering"""
    filters = {}

    # Get filter parameters
    gender = request.GET.get('gender')
    blood_type = request.GET.get('blood_type')
    age_min = request.GET.get('age_min')
    age_max = request.GET.get('age_max')
    has_allergies = request.GET.get('has_allergies')

    # Build queryset
    patients = Patient.objects.filter(is_active=True)

    if gender:
        patients = patients.filter(gender=gender)
    if blood_type:
        patients = patients.filter(blood_type=blood_type)
    if has_allergies == 'true':
        patients = patients.exclude(allergies=[])
    elif has_allergies == 'false':
        patients = patients.filter(allergies=[])

    # Age filtering would require more complex logic
    # For now, we'll skip it as it requires date calculations

    # Serialize results
    results = []
    for patient in patients[:20]:  # Limit to 20 results
        results.append({
            'zimhealth_id': patient.zimhealth_id,
            'full_name': patient.full_name,
            'age': patient.age,
            'gender': patient.get_gender_display(),
            'blood_type': patient.blood_type or 'Unknown',
            'phone_number': patient.phone_number,
            'url': f'/api/patients/{patient.zimhealth_id}/'
        })

    return JsonResponse({
        'patients': results,
        'total_count': patients.count()
    })


@login_required
def dashboard_refresh_stats_ajax(request):
    """AJAX endpoint for refreshing dashboard statistics"""
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    # Calculate comprehensive statistics
    stats = {
        'total_patients': Patient.objects.filter(is_active=True).count(),
        'todays_appointments': Appointment.objects.filter(
            date=today, status='scheduled'
        ).count(),
        'pending_appointments': Appointment.objects.filter(
            status='scheduled', date__gte=today
        ).count(),
        'active_prescriptions': Prescription.objects.filter(status='active').count(),
        'total_records': MedicalRecord.objects.count(),
        'new_records_today': MedicalRecord.objects.filter(date=today).count(),
        'new_records_week': MedicalRecord.objects.filter(date__gte=week_ago).count(),
        'expiring_prescriptions': Prescription.objects.filter(
            status='active',
            end_date__lte=today + timedelta(days=7)
        ).count(),
        'completed_appointments_today': Appointment.objects.filter(
            date=today, status='completed'
        ).count(),
        'new_patients_week': Patient.objects.filter(
            created_at__gte=timezone.now() - timedelta(days=7)
        ).count(),
        'new_patients_month': Patient.objects.filter(
            created_at__gte=timezone.now() - timedelta(days=30)
        ).count(),
        'last_updated': timezone.now().isoformat()
    }

    return JsonResponse(stats)


@login_required
def dashboard_recent_activities_ajax(request):
    """AJAX endpoint for recent activities (alternative endpoint)"""
    limit = int(request.GET.get('limit', 10))
    hours = int(request.GET.get('hours', 24))

    activities = []
    cutoff_time = timezone.now() - timedelta(hours=hours)

    # Get recent activities from different models
    recent_patients = Patient.objects.filter(
        created_at__gte=cutoff_time
    ).order_by('-created_at')[:limit]

    recent_appointments = Appointment.objects.filter(
        updated_at__gte=cutoff_time
    ).select_related('patient').order_by('-updated_at')[:limit]

    recent_records = MedicalRecord.objects.filter(
        created_at__gte=cutoff_time
    ).select_related('patient').order_by('-created_at')[:limit]

    # Combine and format activities
    all_activities = []

    for patient in recent_patients:
        all_activities.append({
            'timestamp': patient.created_at,
            'icon': 'user-plus',
            'title': 'New Patient Registration',
            'description': f'{patient.full_name} registered in the system',
            'type': 'patient',
            'url': f'/api/patients/{patient.zimhealth_id}/'
        })

    for appointment in recent_appointments:
        all_activities.append({
            'timestamp': appointment.updated_at,
            'icon': 'calendar-check',
            'title': f'Appointment {appointment.status.title()}',
            'description': f'{appointment.patient.full_name} - {appointment.doctor_name}',
            'type': 'appointment',
            'url': f'/api/appointments/{appointment.id}/'
        })

    for record in recent_records:
        all_activities.append({
            'timestamp': record.created_at,
            'icon': 'file-medical-alt',
            'title': 'Medical Record Added',
            'description': f'{record.patient.full_name} - {record.diagnosis[:50]}...',
            'type': 'record',
            'url': f'/api/medical-records/{record.id}/'
        })

    # Sort by timestamp and limit results
    all_activities.sort(key=lambda x: x['timestamp'], reverse=True)
    activities = all_activities[:limit]

    # Format timestamps for display
    for activity in activities:
        activity['timestamp'] = activity['timestamp'].isoformat()
        activity['time_ago'] = format_time_ago(activity['timestamp'])

    return JsonResponse({'activities': activities})


def format_time_ago(timestamp_str):
    """Helper function to format time ago"""
    from datetime import datetime
    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
    now = timezone.now()
    diff = now - timestamp.replace(tzinfo=timezone.utc)

    if diff.days > 0:
        return f"{diff.days} day{'s' if diff.days > 1 else ''} ago"
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f"{hours} hour{'s' if hours > 1 else ''} ago"
    elif diff.seconds > 60:
        minutes = diff.seconds // 60
        return f"{minutes} minute{'s' if minutes > 1 else ''} ago"
    else:
        return "Just now"


@login_required
def scan_qr_code_ajax(request):
    """AJAX endpoint for QR code scanning"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            qr_data = data.get('qr_data', '')

            # Try to extract ZimHealth ID from QR data
            if qr_data.startswith('ZH-'):
                zimhealth_id = qr_data
            else:
                # Try to parse JSON QR data
                try:
                    qr_json = json.loads(qr_data)
                    zimhealth_id = qr_json.get('zimhealth_id')
                except:
                    return JsonResponse({
                        'success': False,
                        'error': 'Invalid QR code format'
                    })

            # Find patient
            try:
                patient = Patient.objects.get(zimhealth_id=zimhealth_id, is_active=True)
                return JsonResponse({
                    'success': True,
                    'patient': {
                        'zimhealth_id': patient.zimhealth_id,
                        'full_name': patient.full_name,
                        'age': patient.age,
                        'gender': patient.get_gender_display(),
                        'blood_type': patient.blood_type or 'Unknown',
                        'phone_number': patient.phone_number,
                        'url': f'/api/patients/{patient.zimhealth_id}/'
                    }
                })
            except Patient.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'error': 'Patient not found'
                })

        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Invalid JSON data'
            })

    return JsonResponse({
        'success': False,
        'error': 'Invalid request method'
    })


@login_required
def patient_qr_data_ajax(request, zimhealth_id):
    """AJAX endpoint for patient QR data"""
    try:
        patient = Patient.objects.get(zimhealth_id=zimhealth_id, is_active=True)

        qr_data = {
            'zimhealth_id': patient.zimhealth_id,
            'full_name': patient.full_name,
            'national_id': patient.national_id,
            'date_of_birth': patient.date_of_birth.isoformat() if patient.date_of_birth else None,
            'blood_type': patient.blood_type,
            'emergency_contact': patient.emergency_contact,
            'allergies': patient.allergies,
            'generated_at': timezone.now().isoformat()
        }

        return JsonResponse({
            'success': True,
            'qr_data': qr_data,
            'qr_string': json.dumps(qr_data)
        })

    except Patient.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'Patient not found'
        })


# Notification System Views
@login_required
def notifications_list(request):
    """List user notifications"""
    # For now, return mock notifications
    # In a real system, you'd have a Notification model
    notifications = [
        {
            'id': 1,
            'title': 'New Patient Registration',
            'message': 'Sarah Johnson has been registered in the system',
            'type': 'info',
            'read': False,
            'created_at': timezone.now() - timedelta(minutes=30)
        },
        {
            'id': 2,
            'title': 'Appointment Reminder',
            'message': 'You have 5 appointments scheduled for today',
            'type': 'warning',
            'read': False,
            'created_at': timezone.now() - timedelta(hours=2)
        },
        {
            'id': 3,
            'title': 'System Update',
            'message': 'ZimHealth-ID system has been updated successfully',
            'type': 'success',
            'read': True,
            'created_at': timezone.now() - timedelta(days=1)
        }
    ]

    context = {
        'notifications': notifications,
        'unread_count': sum(1 for n in notifications if not n['read'])
    }

    return render(request, 'api/notifications.html', context)


@login_required
def unread_notifications_count_ajax(request):
    """AJAX endpoint for unread notifications count"""
    # Mock data - in real system, query actual notifications
    unread_count = 3

    return JsonResponse({
        'unread_count': unread_count,
        'has_unread': unread_count > 0
    })


@login_required
@require_POST
def mark_notification_read_ajax(request, notification_id):
    """AJAX endpoint to mark notification as read"""
    # Mock implementation - in real system, update notification model
    return JsonResponse({
        'success': True,
        'message': 'Notification marked as read'
    })


# Analytics Endpoints
@login_required
def patient_demographics_ajax(request):
    """AJAX endpoint for patient demographics data"""
    # Gender distribution
    gender_stats = Patient.objects.filter(is_active=True).values('gender').annotate(
        count=Count('gender')
    )

    # Age groups (approximate)
    total_patients = Patient.objects.filter(is_active=True).count()

    # Blood type distribution
    blood_type_stats = Patient.objects.filter(
        is_active=True, blood_type__isnull=False
    ).values('blood_type').annotate(count=Count('blood_type'))

    demographics = {
        'gender_distribution': [
            {'label': item['gender'], 'value': item['count']}
            for item in gender_stats
        ],
        'age_groups': [
            {'label': '0-18', 'value': int(total_patients * 0.15)},
            {'label': '19-35', 'value': int(total_patients * 0.35)},
            {'label': '36-55', 'value': int(total_patients * 0.30)},
            {'label': '56+', 'value': int(total_patients * 0.20)}
        ],
        'blood_type_distribution': [
            {'label': item['blood_type'], 'value': item['count']}
            for item in blood_type_stats
        ],
        'total_patients': total_patients
    }

    return JsonResponse(demographics)


@login_required
def appointment_trends_ajax(request):
    """AJAX endpoint for appointment trends data"""
    # Get appointment data for the last 30 days
    thirty_days_ago = timezone.now().date() - timedelta(days=30)

    appointments_by_day = Appointment.objects.filter(
        date__gte=thirty_days_ago
    ).values('date').annotate(count=Count('id')).order_by('date')

    # Status distribution
    status_stats = Appointment.objects.values('status').annotate(
        count=Count('status')
    )

    # Type distribution
    type_stats = Appointment.objects.values('appointment_type').annotate(
        count=Count('appointment_type')
    )

    trends = {
        'daily_appointments': [
            {
                'date': item['date'].isoformat(),
                'count': item['count']
            }
            for item in appointments_by_day
        ],
        'status_distribution': [
            {'label': item['status'].title(), 'value': item['count']}
            for item in status_stats
        ],
        'type_distribution': [
            {'label': item['appointment_type'].title(), 'value': item['count']}
            for item in type_stats
        ]
    }

    return JsonResponse(trends)


# Bulk Operations
@login_required
@require_POST
def patients_bulk_action_ajax(request):
    """AJAX endpoint for bulk patient actions"""
    try:
        data = json.loads(request.body)
        action = data.get('action')
        patient_ids = data.get('patient_ids', [])

        if not patient_ids:
            return JsonResponse({
                'success': False,
                'error': 'No patients selected'
            })

        patients = Patient.objects.filter(zimhealth_id__in=patient_ids)

        if action == 'deactivate':
            patients.update(is_active=False)
            message = f'{patients.count()} patients deactivated successfully'
        elif action == 'activate':
            patients.update(is_active=True)
            message = f'{patients.count()} patients activated successfully'
        elif action == 'export':
            # Mock export functionality
            message = f'Export initiated for {patients.count()} patients'
        else:
            return JsonResponse({
                'success': False,
                'error': 'Invalid action'
            })

        return JsonResponse({
            'success': True,
            'message': message,
            'affected_count': patients.count()
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


# Status Update Views
@login_required
@require_POST
def update_appointment_status_ajax(request, appointment_id):
    """AJAX endpoint to update appointment status"""
    try:
        appointment = get_object_or_404(Appointment, id=appointment_id)
        new_status = request.POST.get('status')
        reason = request.POST.get('reason', '')

        if new_status not in ['scheduled', 'completed', 'cancelled', 'no_show']:
            return JsonResponse({
                'success': False,
                'error': 'Invalid status'
            })

        appointment.status = new_status
        if reason:
            appointment.notes = f"{appointment.notes}\n\nStatus changed to {new_status}: {reason}".strip()
        appointment.save()

        return JsonResponse({
            'success': True,
            'message': f'Appointment status updated to {new_status}',
            'new_status': appointment.get_status_display()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
@require_POST
def update_prescription_status_ajax(request, prescription_id):
    """AJAX endpoint to update prescription status"""
    try:
        prescription = get_object_or_404(Prescription, id=prescription_id)
        new_status = request.POST.get('status')

        if new_status not in ['active', 'completed', 'discontinued', 'on_hold']:
            return JsonResponse({
                'success': False,
                'error': 'Invalid status'
            })

        prescription.status = new_status
        prescription.save()

        return JsonResponse({
            'success': True,
            'message': f'Prescription status updated to {new_status}',
            'new_status': prescription.get_status_display()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


# Convenience Views for Creating Records for Specific Patients
@login_required
def medical_record_create_for_patient(request, zimhealth_id):
    """Create medical record for specific patient"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id, is_active=True)

    if request.method == 'POST':
        form = MedicalRecordForm(request.POST)
        if form.is_valid():
            medical_record = form.save(commit=False)
            medical_record.patient = patient
            medical_record.created_by = request.user
            medical_record.save()
            messages.success(request, f'Medical record created successfully for {patient.full_name}.')
            return redirect('api:medical_record_detail', medical_record.id)
    else:
        form = MedicalRecordForm(initial={'patient': patient})

    context = {
        'form': form,
        'patient': patient,
        'title': f'Create Medical Record for {patient.full_name}'
    }
    return render(request, 'api/medical_record_form.html', context)


@login_required
def appointment_create_for_patient(request, zimhealth_id):
    """Create appointment for specific patient"""
    patient = get_object_or_404(Patient, zimhealth_id=zimhealth_id, is_active=True)

    if request.method == 'POST':
        form = AppointmentForm(request.POST)
        if form.is_valid():
            appointment = form.save(commit=False)
            appointment.patient = patient
            appointment.created_by = request.user
            appointment.save()
            messages.success(request, f'Appointment scheduled successfully for {patient.full_name}.')
            return redirect('api:appointment_detail', appointment.id)
    else:
        form = AppointmentForm(initial={'patient': patient})

    context = {
        'form': form,
        'patient': patient,
        'title': f'Schedule Appointment for {patient.full_name}'
    }
    return render(request, 'api/appointment_form.html', context)


@login_required
def prescription_create_for_record(request, record_id):
    """Create prescription for specific medical record"""
    medical_record = get_object_or_404(MedicalRecord, id=record_id)

    if request.method == 'POST':
        form = PrescriptionForm(request.POST)
        if form.is_valid():
            prescription = form.save(commit=False)
            prescription.medical_record = medical_record
            prescription.patient = medical_record.patient
            prescription.prescribed_by = request.user
            prescription.save()
            messages.success(request, f'Prescription added successfully for {medical_record.patient.full_name}.')
            return redirect('api:prescription_detail', prescription.id)
    else:
        form = PrescriptionForm(initial={
            'medical_record': medical_record,
            'patient': medical_record.patient
        })

    context = {
        'form': form,
        'medical_record': medical_record,
        'patient': medical_record.patient,
        'title': f'Add Prescription for {medical_record.patient.full_name}'
    }
    return render(request, 'api/prescription_form.html', context)
