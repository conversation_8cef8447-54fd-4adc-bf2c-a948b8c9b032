/**
 * Professional Patients Page JavaScript for ZimHealth-ID
 * Handles QR code scanning, search, and interactive features
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize patients page functionality
    initPatientsSearch();
    initQRCodeScanner();
    initTableInteractions();
    initFilterFunctionality();
    initScrollAnimations();
    initDemoData();
});

/**
 * Initialize enhanced patient search functionality
 */
function initPatientsSearch() {
    const searchInput = document.getElementById('patient-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            const searchTerm = e.target.value.toLowerCase();
            filterPatients(searchTerm);
        }, 300));
    }
}

/**
 * Filter patients based on search term
 */
function filterPatients(searchTerm) {
    const rows = document.querySelectorAll('.patients-table tbody tr');
    let visibleCount = 0;
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const isVisible = text.includes(searchTerm);
        
        if (isVisible) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    updateResultsCount(visibleCount);
}

/**
 * Initialize QR Code Scanner functionality
 */
function initQRCodeScanner() {
    const qrButton = document.getElementById('qr-scanner-btn');
    const qrModal = document.getElementById('qr-modal');
    const closeQRBtn = document.getElementById('close-qr-btn');
    
    if (qrButton && qrModal) {
        qrButton.addEventListener('click', openQRScanner);
        closeQRBtn?.addEventListener('click', closeQRScanner);
        qrModal.addEventListener('click', function(e) {
            if (e.target === qrModal) closeQRScanner();
        });
    }
}

/**
 * Open QR Code Scanner
 */
async function openQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');
    
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
            video: { facingMode: 'environment' } 
        });
        
        qrVideo.srcObject = stream;
        qrModal.classList.add('active');
        
        showNotification('QR Scanner activated. Point camera at patient QR code.', 'info');
        
        // Simulate QR detection after 3 seconds for demo
        setTimeout(() => {
            if (qrModal.classList.contains('active')) {
                handleQRCodeDetected('ZH-2024-001');
            }
        }, 3000);
        
    } catch (error) {
        console.error('Error accessing camera:', error);
        showNotification('Camera access denied. Please enable camera permissions.', 'error');
    }
}

/**
 * Close QR Code Scanner
 */
function closeQRScanner() {
    const qrModal = document.getElementById('qr-modal');
    const qrVideo = document.getElementById('qr-video');
    
    if (qrVideo.srcObject) {
        const tracks = qrVideo.srcObject.getTracks();
        tracks.forEach(track => track.stop());
        qrVideo.srcObject = null;
    }
    
    qrModal.classList.remove('active');
}

/**
 * Handle QR Code Detection
 */
function handleQRCodeDetected(patientId) {
    closeQRScanner();
    
    const searchInput = document.getElementById('patient-search');
    if (searchInput) {
        searchInput.value = patientId;
        filterPatients(patientId.toLowerCase());
        highlightPatient(patientId);
        showNotification(`Patient found: ${patientId}`, 'success');
    }
}

/**
 * Highlight found patient in table
 */
function highlightPatient(patientId) {
    const rows = document.querySelectorAll('.patients-table tbody tr');
    
    rows.forEach(row => {
        const idCell = row.querySelector('td:nth-child(2)');
        if (idCell && idCell.textContent.trim() === patientId) {
            row.style.background = 'linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05))';
            row.style.border = '2px solid rgba(34, 197, 94, 0.3)';
            row.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            setTimeout(() => {
                row.style.background = '';
                row.style.border = '';
            }, 3000);
        }
    });
}

/**
 * Initialize table interactions
 */
function initTableInteractions() {
    const actionButtons = document.querySelectorAll('.action-button');
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

/**
 * Initialize filter functionality
 */
function initFilterFunctionality() {
    const genderFilter = document.getElementById('gender-filter');
    const bloodTypeFilter = document.getElementById('blood-type-filter');
    
    if (genderFilter) {
        genderFilter.addEventListener('change', applyFilters);
    }
    
    if (bloodTypeFilter) {
        bloodTypeFilter.addEventListener('change', applyFilters);
    }
}

/**
 * Apply filters to patient table
 */
function applyFilters() {
    const genderFilter = document.getElementById('gender-filter')?.value || '';
    const bloodTypeFilter = document.getElementById('blood-type-filter')?.value || '';
    const searchTerm = document.getElementById('patient-search')?.value.toLowerCase() || '';
    
    const rows = document.querySelectorAll('.patients-table tbody tr');
    let visibleCount = 0;
    
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length === 0) return;
        
        const patientText = row.textContent.toLowerCase();
        const genderText = cells[3]?.textContent.toLowerCase() || '';
        const bloodTypeText = cells[4]?.textContent.toLowerCase() || '';
        
        const matchesSearch = !searchTerm || patientText.includes(searchTerm);
        const matchesGender = !genderFilter || genderText.includes(genderFilter.toLowerCase());
        const matchesBloodType = !bloodTypeFilter || bloodTypeText.includes(bloodTypeFilter.toLowerCase());
        
        const isVisible = matchesSearch && matchesGender && matchesBloodType;
        
        if (isVisible) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    updateResultsCount(visibleCount);
}

/**
 * Update results count display
 */
function updateResultsCount(count) {
    const countElement = document.querySelector('.results-count');
    if (countElement) {
        countElement.textContent = `${count} patients`;
    }
}

/**
 * Initialize scroll animations
 */
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.search-filter-card, .patients-table-card').forEach(element => {
        element.classList.add('animate-on-scroll');
        observer.observe(element);
    });
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    const existing = document.querySelector('.patients-notification');
    if (existing) existing.remove();
    
    const notification = document.createElement('div');
    const typeConfig = {
        success: { bg: 'bg-health-500', icon: 'fa-check-circle' },
        error: { bg: 'bg-red-500', icon: 'fa-exclamation-circle' },
        warning: { bg: 'bg-orange-500', icon: 'fa-exclamation-triangle' },
        info: { bg: 'bg-medical-500', icon: 'fa-info-circle' }
    };
    
    const config = typeConfig[type] || typeConfig.info;
    
    notification.className = `patients-notification fixed top-20 right-4 z-50 p-4 rounded-xl shadow-2xl max-w-sm transform translate-x-full transition-all duration-500 ${config.bg} text-white backdrop-blur-lg`;
    
    notification.innerHTML = `
        <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
                <i class="fas ${config.icon} text-lg"></i>
            </div>
            <div class="flex-1">
                <p class="font-semibold text-sm">${message}</p>
                <p class="text-xs opacity-90 mt-1">ZimHealth-ID Patients</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-1 transition-colors">
                <i class="fas fa-times text-sm"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(100%) scale(0.9)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 500);
    }, 5000);
}

/**
 * Get blood type CSS class for color coding
 */
function getBloodTypeClass(bloodType) {
    const bloodTypeMap = {
        'A+': 'a-positive',
        'A-': 'a-negative',
        'B+': 'b-positive',
        'B-': 'b-negative',
        'AB+': 'ab-positive',
        'AB-': 'ab-negative',
        'O+': 'o-positive',
        'O-': 'o-negative'
    };
    return bloodTypeMap[bloodType] || 'a-positive';
}

/**
 * Utility function to debounce events
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Initialize demo data if no patients exist
 */
function initDemoData() {
    const tbody = document.querySelector('.patients-table tbody');
    const emptyRow = tbody?.querySelector('td[colspan="7"]');

    if (emptyRow) {
        // Create demo patients
        const demoPatients = [
            {
                name: 'Sarah Johnson',
                nationalId: '63-123456-A12',
                zimhealthId: 'ZH-2024-001',
                phone: '+263 77 123 4567',
                address: 'Harare, Zimbabwe',
                age: '32',
                gender: 'Female',
                bloodType: 'A+',
                lastVisit: 'Jan 15, 2025'
            },
            {
                name: 'Michael Brown',
                nationalId: '63-234567-B23',
                zimhealthId: 'ZH-2024-002',
                phone: '+263 77 234 5678',
                address: 'Bulawayo, Zimbabwe',
                age: '45',
                gender: 'Male',
                bloodType: 'O+',
                lastVisit: 'Jan 12, 2025'
            },
            {
                name: 'Emma Wilson',
                nationalId: '63-345678-C34',
                zimhealthId: 'ZH-2024-003',
                phone: '+263 77 345 6789',
                address: 'Mutare, Zimbabwe',
                age: '28',
                gender: 'Female',
                bloodType: 'B+',
                lastVisit: 'Jan 10, 2025'
            },
            {
                name: 'David Martinez',
                nationalId: '63-456789-D45',
                zimhealthId: 'ZH-2024-004',
                phone: '+263 77 456 7890',
                address: 'Gweru, Zimbabwe',
                age: '38',
                gender: 'Male',
                bloodType: 'AB+',
                lastVisit: 'Jan 08, 2025'
            },
            {
                name: 'Lisa Chen',
                nationalId: '63-567890-E56',
                zimhealthId: 'ZH-2024-005',
                phone: '+263 77 567 8901',
                address: 'Masvingo, Zimbabwe',
                age: '29',
                gender: 'Female',
                bloodType: 'O-',
                lastVisit: 'Jan 05, 2025'
            }
        ];

        // Replace empty state with demo patients
        tbody.innerHTML = demoPatients.map(patient => `
            <tr>
                <td>
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="patient-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-semibold text-gray-900">${patient.name}</div>
                            <div class="text-sm text-gray-500">${patient.nationalId}</div>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="text-sm font-mono font-semibold text-gray-900">${patient.zimhealthId}</div>
                </td>
                <td>
                    <div class="text-sm text-gray-900">${patient.phone}</div>
                    <div class="text-sm text-gray-500">${patient.address}</div>
                </td>
                <td>
                    <div class="text-sm text-gray-900">${patient.age} years</div>
                    <div class="text-sm text-gray-500">${patient.gender}</div>
                </td>
                <td>
                    <span class="blood-type-badge ${getBloodTypeClass(patient.bloodType)}">${patient.bloodType}</span>
                </td>
                <td>
                    <div class="text-sm text-gray-500">${patient.lastVisit}</div>
                </td>
                <td>
                    <div class="flex items-center space-x-2">
                        <a href="#" class="action-button view" title="View Details">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="#" class="action-button edit" title="Edit Patient">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="#" class="action-button medical" title="Medical Record">
                            <i class="fas fa-file-medical"></i>
                        </a>
                        <a href="#" class="action-button appointment" title="Schedule Appointment">
                            <i class="fas fa-calendar"></i>
                        </a>
                    </div>
                </td>
            </tr>
        `).join('');

        // Update results count
        updateResultsCount(demoPatients.length);

        // Re-initialize interactions
        initTableInteractions();
    }
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
    }

    .animate-on-scroll.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
`;
document.head.appendChild(style);
