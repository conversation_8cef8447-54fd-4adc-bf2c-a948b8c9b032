/**
 * Professional Appointments Page JavaScript for ZimHealth-ID
 * Handles search, filtering, and interactive features
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize appointments page functionality
    initAppointmentsSearch();
    initQRCodeScanner();
    initFilterFunctionality();
    initViewToggle();
    initActionButtons();
    initScrollAnimations();
    initDemoData();
});

/**
 * Initialize enhanced appointment search functionality
 */
function initAppointmentsSearch() {
    const searchInput = document.getElementById('appointment-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            const searchTerm = e.target.value.toLowerCase();
            filterAppointments(searchTerm);
        }, 300));
    }
}

/**
 * Initialize QR Code Scanner functionality
 */
function initQRCodeScanner() {
    const qrButton = document.getElementById('appointment-qr-scanner-btn');
    if (qrButton) {
        qrButton.addEventListener('click', function(e) {
            e.preventDefault();

            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);

            // Check if browser supports camera access
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                startQRScanner();
            } else {
                showNotification('Camera access not supported in this browser', 'error');
            }
        });
    }
}

/**
 * Start QR Code Scanner
 */
function startQRScanner() {
    showNotification('QR Scanner starting...', 'info');

    // Simulate QR scanning process
    setTimeout(() => {
        // For demo purposes, simulate a successful scan
        const demoPatientId = 'ZH-2024-001';
        const searchInput = document.getElementById('appointment-search');

        if (searchInput) {
            searchInput.value = demoPatientId;
            filterAppointments(demoPatientId.toLowerCase());
            showNotification(`QR Code scanned successfully! Found appointments for ${demoPatientId}`, 'success');
        }
    }, 2000);

    // In a real implementation, you would:
    // 1. Access the camera using navigator.mediaDevices.getUserMedia()
    // 2. Use a QR code library like jsQR or QuaggaJS
    // 3. Process the video stream to detect QR codes
    // 4. Extract patient ID from the QR code
    // 5. Search for appointments using the extracted ID
}

/**
 * Filter appointments based on search term
 */
function filterAppointments(searchTerm) {
    const appointments = document.querySelectorAll('.appointments-table tbody tr:not(.notes-row)');
    let visibleCount = 0;

    appointments.forEach(appointment => {
        const text = appointment.textContent.toLowerCase();
        const isVisible = text.includes(searchTerm);

        // Handle notes row visibility
        const nextRow = appointment.nextElementSibling;
        const isNotesRow = nextRow && nextRow.classList.contains('notes-row');

        if (isVisible) {
            appointment.style.display = '';
            if (isNotesRow) nextRow.style.display = '';
            visibleCount++;
        } else {
            appointment.style.display = 'none';
            if (isNotesRow) nextRow.style.display = 'none';
        }
    });

    updateResultsCount(visibleCount);
}

/**
 * Initialize filter functionality
 */
function initFilterFunctionality() {
    const statusFilter = document.getElementById('status-filter');
    const typeFilter = document.getElementById('type-filter');
    const dateFilter = document.getElementById('date-filter');
    
    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }
    
    if (typeFilter) {
        typeFilter.addEventListener('change', applyFilters);
    }
    
    if (dateFilter) {
        dateFilter.addEventListener('change', applyFilters);
    }
}

/**
 * Apply filters to appointment list
 */
function applyFilters() {
    const statusFilter = document.getElementById('status-filter')?.value || '';
    const typeFilter = document.getElementById('type-filter')?.value || '';
    const dateFilter = document.getElementById('date-filter')?.value || '';
    const searchTerm = document.getElementById('appointment-search')?.value.toLowerCase() || '';

    const appointments = document.querySelectorAll('.appointments-table tbody tr:not(.notes-row)');
    let visibleCount = 0;

    appointments.forEach(appointment => {
        const appointmentText = appointment.textContent.toLowerCase();
        const statusText = appointment.querySelector('.status-badge')?.textContent.toLowerCase() || '';
        const typeText = appointment.querySelector('.type-badge')?.textContent.toLowerCase() || '';
        const dateText = appointment.querySelector('.appointment-date')?.textContent || '';

        const matchesSearch = !searchTerm || appointmentText.includes(searchTerm);
        const matchesStatus = !statusFilter || statusText.includes(statusFilter.toLowerCase());
        const matchesType = !typeFilter || typeText.includes(typeFilter.toLowerCase());
        const matchesDate = !dateFilter || dateText.includes(dateFilter);

        const isVisible = matchesSearch && matchesStatus && matchesType && matchesDate;

        // Handle notes row visibility
        const nextRow = appointment.nextElementSibling;
        const isNotesRow = nextRow && nextRow.classList.contains('notes-row');

        if (isVisible) {
            appointment.style.display = '';
            if (isNotesRow) nextRow.style.display = '';
            visibleCount++;
        } else {
            appointment.style.display = 'none';
            if (isNotesRow) nextRow.style.display = 'none';
        }
    });

    updateResultsCount(visibleCount);
}

/**
 * Initialize view toggle functionality
 */
function initViewToggle() {
    const listViewBtn = document.getElementById('list-view-btn');
    const calendarViewBtn = document.getElementById('calendar-view-btn');
    
    if (listViewBtn && calendarViewBtn) {
        listViewBtn.addEventListener('click', function() {
            setActiveView('list');
        });
        
        calendarViewBtn.addEventListener('click', function() {
            setActiveView('calendar');
        });
    }
}

/**
 * Set active view (list or calendar)
 */
function setActiveView(view) {
    const listViewBtn = document.getElementById('list-view-btn');
    const calendarViewBtn = document.getElementById('calendar-view-btn');
    const appointmentsList = document.querySelector('.appointments-list');
    const calendarView = document.querySelector('.calendar-view');
    
    if (view === 'list') {
        listViewBtn?.classList.add('active');
        calendarViewBtn?.classList.remove('active');
        if (appointmentsList) appointmentsList.style.display = 'block';
        if (calendarView) calendarView.style.display = 'none';
    } else {
        calendarViewBtn?.classList.add('active');
        listViewBtn?.classList.remove('active');
        if (appointmentsList) appointmentsList.style.display = 'none';
        if (calendarView) calendarView.style.display = 'block';
    }
}

/**
 * Initialize action buttons
 */
function initActionButtons() {
    const actionButtons = document.querySelectorAll('.appointment-action-button');
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
            
            // Handle different actions
            const action = this.getAttribute('data-action');
            const appointmentId = this.getAttribute('data-appointment-id');
            
            switch(action) {
                case 'complete':
                    handleCompleteAppointment(appointmentId);
                    break;
                case 'reschedule':
                    handleRescheduleAppointment(appointmentId);
                    break;
                case 'cancel':
                    handleCancelAppointment(appointmentId);
                    break;
                case 'view':
                    handleViewAppointment(appointmentId);
                    break;
                case 'edit':
                    handleEditAppointment(appointmentId);
                    break;
            }
        });
    });
}

/**
 * Handle appointment actions
 */
function handleCompleteAppointment(appointmentId) {
    showNotification('Appointment marked as completed', 'success');
}

function handleRescheduleAppointment(appointmentId) {
    showNotification('Reschedule functionality coming soon', 'info');
}

function handleCancelAppointment(appointmentId) {
    if (confirm('Are you sure you want to cancel this appointment?')) {
        showNotification('Appointment cancelled', 'warning');
    }
}

function handleViewAppointment(appointmentId) {
    showNotification('Opening appointment details', 'info');
}

function handleEditAppointment(appointmentId) {
    showNotification('Opening appointment editor', 'info');
}

/**
 * Update results count display
 */
function updateResultsCount(count) {
    const countElement = document.querySelector('.results-count');
    if (countElement) {
        countElement.textContent = `${count} appointments`;
    }
}

/**
 * Initialize scroll animations
 */
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe cards and appointment items
    document.querySelectorAll('.stats-card, .appointments-table-card').forEach(element => {
        element.classList.add('animate-on-scroll');
        observer.observe(element);
    });
}

/**
 * Initialize demo data if no appointments exist
 */
function initDemoData() {
    const emptyState = document.querySelector('td[colspan="6"]');
    const appointmentsTBody = document.querySelector('.appointments-table tbody');

    if (emptyState && appointmentsTBody) {
        // Create demo appointments
        const demoAppointments = [
            {
                id: 'APT-001',
                patientName: 'Sarah Johnson',
                patientId: 'ZH-2024-001',
                date: 'Jul 29, 2025',
                time: '09:00',
                doctor: 'Dr. Smith',
                facility: 'Central Hospital',
                type: 'Consultation',
                status: 'scheduled',
                priority: 'normal',
                reason: 'Regular checkup and blood pressure monitoring'
            },
            {
                id: 'APT-002',
                patientName: 'Michael Brown',
                patientId: 'ZH-2024-002',
                date: 'Jul 29, 2025',
                time: '10:30',
                doctor: 'Dr. Wilson',
                facility: 'Medical Center',
                type: 'Follow-up',
                status: 'completed',
                priority: 'normal',
                reason: 'Post-surgery follow-up examination'
            },
            {
                id: 'APT-003',
                patientName: 'Emma Wilson',
                patientId: 'ZH-2024-003',
                date: 'Jul 30, 2025',
                time: '14:00',
                doctor: 'Dr. Martinez',
                facility: 'Health Clinic',
                type: 'Screening',
                status: 'scheduled',
                priority: 'high',
                reason: 'Annual health screening and vaccination'
            }
        ];
        
        // Replace empty state with demo appointments
        appointmentsTBody.innerHTML = demoAppointments.map(appointment =>
            createAppointmentTableRow(appointment)
        ).join('');

        // Update results count
        updateResultsCount(demoAppointments.length);

        // Re-initialize interactions
        initActionButtons();
    }
}

/**
 * Create appointment table row HTML
 */
function createAppointmentTableRow(appointment) {
    return `
        <tr>
            <!-- Patient Column -->
            <td class="patient-column">
                <div class="flex items-center space-x-3">
                    <div class="status-indicator ${appointment.status}"></div>
                    <div class="appointment-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <div class="text-sm font-semibold text-gray-900">${appointment.patientName}</div>
                        <div class="text-xs text-gray-500">${appointment.patientId}</div>
                    </div>
                </div>
            </td>

            <!-- Date & Time Column -->
            <td class="datetime-column">
                <div class="text-sm font-medium text-gray-900 appointment-date">${appointment.date}</div>
                <div class="text-xs text-gray-500">${appointment.time}</div>
            </td>

            <!-- Healthcare Provider Column -->
            <td class="doctor-column">
                <div class="text-sm font-medium text-gray-900">${appointment.doctor}</div>
                <div class="text-xs text-gray-500">${appointment.facility}</div>
            </td>

            <!-- Type Column -->
            <td class="type-column">
                <span class="type-badge">${appointment.type}</span>
                ${appointment.priority !== 'normal' ? `<div class="priority-indicator ${appointment.priority} mt-1 text-xs">${appointment.priority.toUpperCase()}</div>` : ''}
            </td>

            <!-- Status Column -->
            <td class="status-column">
                <span class="status-badge ${appointment.status}">${appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}</span>
            </td>

            <!-- Actions Column -->
            <td class="actions-column">
                <div class="flex items-center justify-center space-x-1">
                    ${appointment.status === 'scheduled' ? `
                        <button class="appointment-action-button complete" data-action="complete" data-appointment-id="${appointment.id}" title="Mark Complete">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="appointment-action-button reschedule" data-action="reschedule" data-appointment-id="${appointment.id}" title="Reschedule">
                            <i class="fas fa-calendar-alt"></i>
                        </button>
                        <button class="appointment-action-button cancel" data-action="cancel" data-appointment-id="${appointment.id}" title="Cancel">
                            <i class="fas fa-times"></i>
                        </button>
                    ` : ''}
                    <button class="appointment-action-button" data-action="view" data-appointment-id="${appointment.id}" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="appointment-action-button" data-action="edit" data-appointment-id="${appointment.id}" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </td>
        </tr>
        ${appointment.reason ? `
            <tr class="notes-row">
                <td colspan="6" class="px-6 py-2 bg-gray-50 border-b border-gray-200">
                    <p class="text-xs text-gray-600"><strong>Reason:</strong> ${appointment.reason}</p>
                </td>
            </tr>
        ` : ''}
    `;
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existing = document.querySelector('.appointments-notification');
    if (existing) existing.remove();
    
    const notification = document.createElement('div');
    const typeConfig = {
        success: { bg: 'bg-health-500', icon: 'fa-check-circle' },
        error: { bg: 'bg-red-500', icon: 'fa-exclamation-circle' },
        warning: { bg: 'bg-orange-500', icon: 'fa-exclamation-triangle' },
        info: { bg: 'bg-medical-500', icon: 'fa-info-circle' }
    };
    
    const config = typeConfig[type] || typeConfig.info;
    
    notification.className = `appointments-notification fixed top-20 right-4 z-50 p-4 rounded-xl shadow-2xl max-w-sm transform translate-x-full transition-all duration-500 ${config.bg} text-white backdrop-blur-lg`;
    
    notification.innerHTML = `
        <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
                <i class="fas ${config.icon} text-lg"></i>
            </div>
            <div class="flex-1">
                <p class="font-semibold text-sm">${message}</p>
                <p class="text-xs opacity-90 mt-1">ZimHealth-ID Appointments</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-1 transition-colors">
                <i class="fas fa-times text-sm"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%) scale(0.9)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 500);
    }, 5000);
}

/**
 * Utility function to debounce events
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
    }
    
    .animate-on-scroll.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
`;
document.head.appendChild(style);
