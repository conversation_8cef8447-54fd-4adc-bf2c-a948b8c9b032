/**
 * Professional Dashboard JavaScript for ZimHealth-ID
 * Handles interactive features and animations
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    // Initialize dashboard functionality
    initStatCardAnimations();
    initQuickActionInteractions();
    initActivityTimeline();
    
    if (!prefersReducedMotion) {
        initScrollAnimations();
        initCounterAnimations();
        initCardHoverEffects();
    }
});

/**
 * Initialize statistics card animations
 */
function initStatCardAnimations() {
    const statCards = document.querySelectorAll('.stat-card');
    
    statCards.forEach((card, index) => {
        // Stagger the initial animation
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate-fade-in-up');
        
        // Add click interaction for detailed view
        card.addEventListener('click', function() {
            const cardType = this.classList.contains('patients') ? 'patients' :
                           this.classList.contains('appointments') ? 'appointments' :
                           this.classList.contains('records') ? 'records' : 'prescriptions';
            
            showDetailedView(cardType);
        });
    });
}

/**
 * Initialize enhanced quick action interactions
 */
function initQuickActionInteractions() {
    const quickActions = document.querySelectorAll('.quick-action');

    quickActions.forEach((action, index) => {
        // Stagger initial animations
        action.style.animationDelay = `${index * 0.1}s`;
        action.classList.add('animate-fade-in-up');

        action.addEventListener('click', function(e) {
            e.preventDefault();

            // Enhanced click animation with ripple effect
            createRippleEffect(this);

            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);

            // Show action feedback with enhanced styling
            const actionText = this.querySelector('span').textContent;
            const actionType = this.classList.contains('medical') ? 'medical' :
                             this.classList.contains('health') ? 'health' :
                             this.classList.contains('blue') ? 'info' :
                             this.classList.contains('purple') ? 'info' :
                             this.classList.contains('orange') ? 'warning' :
                             this.classList.contains('red') ? 'error' : 'info';

            showNotification(`${actionText} module launching soon!`, actionType);
        });

        // Add hover sound effect (optional)
        action.addEventListener('mouseenter', function() {
            // Subtle hover feedback
            this.style.boxShadow = '0 15px 35px rgba(14, 165, 233, 0.2)';
        });

        action.addEventListener('mouseleave', function() {
            this.style.boxShadow = '';
        });
    });
}

/**
 * Create ripple effect for enhanced interactions
 */
function createRippleEffect(element) {
    const ripple = document.createElement('div');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);

    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = (rect.width / 2 - size / 2) + 'px';
    ripple.style.top = (rect.height / 2 - size / 2) + 'px';
    ripple.style.position = 'absolute';
    ripple.style.borderRadius = '50%';
    ripple.style.background = 'rgba(14, 165, 233, 0.3)';
    ripple.style.transform = 'scale(0)';
    ripple.style.animation = 'ripple 0.6s linear';
    ripple.style.pointerEvents = 'none';

    element.style.position = 'relative';
    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * Initialize activity timeline interactions
 */
function initActivityTimeline() {
    const activityItems = document.querySelectorAll('.activity-item');
    
    activityItems.forEach(item => {
        item.addEventListener('click', function() {
            // Highlight selected activity
            activityItems.forEach(i => i.classList.remove('selected'));
            this.classList.add('selected');
            
            // Add selection styling
            this.style.background = 'linear-gradient(135deg, rgba(14, 165, 233, 0.05), rgba(34, 197, 94, 0.03))';
            this.style.borderLeft = '3px solid #0ea5e9';
        });
    });
}

/**
 * Initialize scroll-triggered animations
 */
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe content cards
    document.querySelectorAll('.content-card').forEach(card => {
        card.classList.add('animate-on-scroll');
        observer.observe(card);
    });
}

/**
 * Initialize counter animations for statistics
 */
function initCounterAnimations() {
    const statNumbers = document.querySelectorAll('.stat-card .text-2xl');
    
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                counterObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });
    
    statNumbers.forEach(number => {
        counterObserver.observe(number);
    });
}

/**
 * Animate counter numbers
 */
function animateCounter(element) {
    const text = element.textContent;
    const number = parseInt(text.replace(/[^\d]/g, ''));
    const suffix = text.replace(/[\d]/g, '');
    
    if (isNaN(number)) return;
    
    const duration = 2000;
    const startTime = performance.now();
    
    function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const current = Math.floor(number * easeOutQuart(progress));
        
        if (number >= 1000) {
            element.textContent = (current / 1000).toFixed(1) + 'K' + suffix.replace(/[\d.K]/g, '');
        } else {
            element.textContent = current + suffix.replace(/[\d]/g, '');
        }
        
        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        }
    }
    
    requestAnimationFrame(updateCounter);
}

/**
 * Easing function for smooth animations
 */
function easeOutQuart(t) {
    return 1 - (--t) * t * t * t;
}

/**
 * Initialize card hover effects
 */
function initCardHoverEffects() {
    const cards = document.querySelectorAll('.stat-card, .content-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
}

/**
 * Show detailed view for statistics
 */
function showDetailedView(type) {
    const titles = {
        patients: 'Patient Management',
        appointments: 'Appointment Schedule',
        records: 'Medical Records',
        prescriptions: 'Prescription Management'
    };
    
    showNotification(`${titles[type]} detailed view coming soon!`, 'info');
}

/**
 * Enhanced notification system with multiple types
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existing = document.querySelector('.dashboard-notification');
    if (existing) existing.remove();

    const notification = document.createElement('div');
    const typeConfig = {
        medical: { bg: 'bg-medical-500', icon: 'fa-user-md' },
        health: { bg: 'bg-health-500', icon: 'fa-heartbeat' },
        success: { bg: 'bg-health-500', icon: 'fa-check-circle' },
        error: { bg: 'bg-red-500', icon: 'fa-exclamation-circle' },
        warning: { bg: 'bg-orange-500', icon: 'fa-exclamation-triangle' },
        info: { bg: 'bg-medical-500', icon: 'fa-info-circle' }
    };

    const config = typeConfig[type] || typeConfig.info;

    notification.className = `dashboard-notification fixed top-20 right-4 z-50 p-4 rounded-xl shadow-2xl max-w-sm transform translate-x-full transition-all duration-500 ${config.bg} text-white backdrop-blur-lg`;

    notification.innerHTML = `
        <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
                <i class="fas ${config.icon} text-lg"></i>
            </div>
            <div class="flex-1">
                <p class="font-semibold text-sm">${message}</p>
                <p class="text-xs opacity-90 mt-1">ZimHealth-ID System</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-1 transition-colors">
                <i class="fas fa-times text-sm"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in with enhanced effect
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
        notification.style.transform = 'translateX(0) scale(1)';
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%) scale(0.9)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 500);
    }, 5000);
}

/**
 * Real-time clock update
 */
function initRealTimeClock() {
    const clockElement = document.querySelector('.dashboard-clock');
    if (clockElement) {
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit'
            });
            clockElement.textContent = timeString;
        }
        
        updateClock();
        setInterval(updateClock, 1000);
    }
}

/**
 * Initialize search functionality
 */
function initDashboardSearch() {
    const searchInput = document.querySelector('.dashboard-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function() {
            const query = this.value.toLowerCase();
            performQuickPatientSearch(query);
        }, 300));
    }
}

/**
 * Perform quick patient search
 */
function performQuickPatientSearch(query) {
    if (query.length < 2) {
        hideSearchResults();
        return;
    }

    fetch(`/api/ajax/quick-patient-search/?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data.patients);
        })
        .catch(error => {
            console.error('Search error:', error);
            showNotification('Search failed. Please try again.', 'error');
        });
}

/**
 * Display search results
 */
function displaySearchResults(patients) {
    let searchResults = document.getElementById('search-results');

    if (!searchResults) {
        searchResults = document.createElement('div');
        searchResults.id = 'search-results';
        searchResults.className = 'absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto';

        const searchContainer = document.querySelector('.dashboard-search').parentElement;
        searchContainer.style.position = 'relative';
        searchContainer.appendChild(searchResults);
    }

    if (patients.length === 0) {
        searchResults.innerHTML = '<div class="p-4 text-center text-gray-500">No patients found</div>';
        return;
    }

    searchResults.innerHTML = patients.map(patient => `
        <div class="p-3 hover:bg-gray-50 border-b border-gray-100 cursor-pointer" onclick="window.location.href='${patient.url}'">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-medical-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-medical-600"></i>
                </div>
                <div class="flex-1">
                    <div class="font-semibold text-gray-900">${patient.full_name}</div>
                    <div class="text-sm text-gray-500">${patient.zimhealth_id} • ${patient.age} years • ${patient.gender}</div>
                </div>
                <div class="text-right">
                    <div class="text-sm font-medium text-gray-900">${patient.blood_type}</div>
                    <div class="text-xs text-gray-500">Last visit: ${patient.last_visit}</div>
                </div>
            </div>
        </div>
    `).join('');
}

/**
 * Hide search results
 */
function hideSearchResults() {
    const searchResults = document.getElementById('search-results');
    if (searchResults) {
        searchResults.remove();
    }
}

/**
 * Initialize real-time dashboard updates
 */
function initRealTimeUpdates() {
    // Update statistics every 30 seconds
    setInterval(updateDashboardStats, 30000);

    // Update activities every 60 seconds
    setInterval(updateRecentActivities, 60000);
}

/**
 * Update dashboard statistics
 */
function updateDashboardStats() {
    fetch('/api/ajax/dashboard/refresh-stats/')
        .then(response => response.json())
        .then(data => {
            // Update stat cards
            updateStatCard('total_patients', data.total_patients);
            updateStatCard('todays_appointments', data.todays_appointments);
            updateStatCard('total_records', data.total_records);
            updateStatCard('active_prescriptions', data.active_prescriptions);

            // Update secondary stats
            updateSecondaryStats(data);
        })
        .catch(error => {
            console.error('Failed to update stats:', error);
        });
}

/**
 * Update individual stat card
 */
function updateStatCard(type, value) {
    const statCard = document.querySelector(`.stat-card.${type} .text-2xl`);
    if (statCard) {
        // Animate the number change
        animateNumberChange(statCard, value);
    }
}

/**
 * Animate number change in stat cards
 */
function animateNumberChange(element, newValue) {
    const currentValue = parseInt(element.textContent.replace(/[^\d]/g, '')) || 0;
    const difference = newValue - currentValue;

    if (difference === 0) return;

    const duration = 1000;
    const steps = 20;
    const stepValue = difference / steps;
    const stepDuration = duration / steps;

    let currentStep = 0;

    const interval = setInterval(() => {
        currentStep++;
        const displayValue = Math.round(currentValue + (stepValue * currentStep));

        if (displayValue >= 1000) {
            element.textContent = (displayValue / 1000).toFixed(1) + 'K';
        } else {
            element.textContent = displayValue.toLocaleString();
        }

        if (currentStep >= steps) {
            clearInterval(interval);
            element.textContent = newValue >= 1000 ? (newValue / 1000).toFixed(1) + 'K' : newValue.toLocaleString();
        }
    }, stepDuration);
}

/**
 * Update secondary stats
 */
function updateSecondaryStats(data) {
    // Update pending appointments
    const pendingElement = document.querySelector('.stat-card.appointments .text-sm span');
    if (pendingElement) {
        pendingElement.textContent = `${data.pending_appointments} pending`;
    }

    // Update new records
    const recordsElement = document.querySelector('.stat-card.records .text-sm span');
    if (recordsElement) {
        recordsElement.textContent = `${data.new_records_week} this week`;
    }

    // Update expiring prescriptions
    const prescriptionsElement = document.querySelector('.stat-card.prescriptions .text-sm span');
    if (prescriptionsElement) {
        prescriptionsElement.textContent = `${data.expiring_prescriptions} expiring soon`;
    }
}

/**
 * Update recent activities
 */
function updateRecentActivities() {
    fetch('/api/ajax/dashboard/recent-activities/')
        .then(response => response.json())
        .then(data => {
            displayRecentActivities(data.activities);
        })
        .catch(error => {
            console.error('Failed to update activities:', error);
        });
}

/**
 * Display recent activities
 */
function displayRecentActivities(activities) {
    const activityContent = document.querySelector('.activity-content');
    if (!activityContent || activities.length === 0) return;

    activityContent.innerHTML = `
        <div class="space-y-0">
            ${activities.map(activity => `
                <div class="activity-item flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="activity-icon">
                            <i class="fas fa-${activity.icon} text-medical-600 text-sm"></i>
                        </div>
                    </div>
                    <div class="activity-details">
                        <p class="activity-title-text">${activity.title}</p>
                        <p class="activity-description">${activity.description}</p>
                        <p class="activity-timestamp">${activity.time_ago}</p>
                    </div>
                </div>
            `).join('')}
        </div>
    `;

    // Re-initialize activity interactions
    initActivityTimeline();
}

/**
 * Utility function to debounce events
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize additional features
document.addEventListener('DOMContentLoaded', function() {
    initRealTimeClock();
    initDashboardSearch();
    initActivityDemo();
    initRealTimeUpdates();
    initCommandCenter();

    // Hide search results when clicking outside
    document.addEventListener('click', function(e) {
        const searchContainer = document.querySelector('.dashboard-search')?.parentElement;
        if (searchContainer && !searchContainer.contains(e.target)) {
            hideSearchResults();
        }
    });
});

/**
 * Initialize command center functionality
 */
function initCommandCenter() {
    // Initialize system health monitoring
    initSystemHealthMonitoring();

    // Initialize alert management
    initAlertManagement();

    // Initialize performance metrics
    initPerformanceMetrics();

    // Initialize keyboard shortcuts
    initKeyboardShortcuts();
}

/**
 * System health monitoring
 */
function initSystemHealthMonitoring() {
    // Update system status every 60 seconds
    setInterval(updateSystemHealth, 60000);

    // Initial health check
    updateSystemHealth();
}

function updateSystemHealth() {
    fetch('/api/ajax/dashboard/system-status/')
        .then(response => response.json())
        .then(data => {
            updateHealthIndicators(data);
        })
        .catch(error => {
            console.error('Failed to update system health:', error);
            showSystemAlert('System health check failed', 'error');
        });
}

function updateHealthIndicators(healthData) {
    const indicators = document.querySelectorAll('.bg-white.bg-opacity-20 .rounded-full');

    // Update database status
    if (indicators[0]) {
        indicators[0].className = `w-3 h-3 rounded-full mx-auto mb-2 pulse ${
            healthData.database_status === 'healthy' ? 'bg-green-400' : 'bg-red-400'
        }`;
    }

    // Update server status
    if (indicators[1]) {
        indicators[1].className = `w-3 h-3 rounded-full mx-auto mb-2 pulse ${
            healthData.server_load === 'normal' ? 'bg-green-400' : 'bg-yellow-400'
        }`;
    }

    // Update response time indicator
    if (indicators[2]) {
        const responseTime = parseInt(healthData.response_time);
        indicators[2].className = `w-3 h-3 rounded-full mx-auto mb-2 pulse ${
            responseTime < 200 ? 'bg-green-400' : responseTime < 500 ? 'bg-yellow-400' : 'bg-red-400'
        }`;
    }
}

/**
 * Alert management system
 */
function initAlertManagement() {
    // Check for critical alerts every 5 minutes
    setInterval(checkCriticalAlerts, 300000);
}

function checkCriticalAlerts() {
    fetch('/api/ajax/dashboard/critical-alerts/')
        .then(response => response.json())
        .then(data => {
            if (data.alerts && data.alerts.length > 0) {
                showCriticalAlerts(data.alerts);
            }
        })
        .catch(error => {
            console.error('Failed to check critical alerts:', error);
        });
}

function showCriticalAlerts(alerts) {
    alerts.forEach(alert => {
        if (alert.priority === 'critical') {
            showSystemAlert(alert.message, 'error', true);
        }
    });
}

function showAlertDetails() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-gray-900">Critical Alert Details</h3>
                <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="alert-details-content">
                <div class="text-center py-4">
                    <i class="fas fa-spinner fa-spin text-medical-600 text-2xl mb-2"></i>
                    <p class="text-gray-600">Loading alert details...</p>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Load detailed alert information
    loadAlertDetails();
}

function loadAlertDetails() {
    fetch('/api/ajax/dashboard/alert-details/')
        .then(response => response.json())
        .then(data => {
            const content = document.getElementById('alert-details-content');
            if (content) {
                content.innerHTML = `
                    <div class="space-y-4">
                        ${data.overdue_appointments ? `
                            <div class="border-l-4 border-red-400 pl-4">
                                <h4 class="font-semibold text-red-800">Overdue Appointments (${data.overdue_appointments.length})</h4>
                                <div class="mt-2 space-y-2">
                                    ${data.overdue_appointments.map(apt => `
                                        <div class="text-sm">
                                            <span class="font-medium">${apt.patient_name}</span> -
                                            <span class="text-gray-600">${apt.date} with ${apt.doctor}</span>
                                            <a href="/api/appointments/${apt.id}/" class="text-red-600 hover:text-red-700 ml-2">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}

                        ${data.expiring_prescriptions ? `
                            <div class="border-l-4 border-yellow-400 pl-4">
                                <h4 class="font-semibold text-yellow-800">Expiring Prescriptions (${data.expiring_prescriptions.length})</h4>
                                <div class="mt-2 space-y-2">
                                    ${data.expiring_prescriptions.map(rx => `
                                        <div class="text-sm">
                                            <span class="font-medium">${rx.patient_name}</span> -
                                            <span class="text-gray-600">${rx.medication} expires ${rx.end_date}</span>
                                            <a href="/api/prescriptions/${rx.id}/" class="text-yellow-600 hover:text-yellow-700 ml-2">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}

                        ${data.critical_patients ? `
                            <div class="border-l-4 border-orange-400 pl-4">
                                <h4 class="font-semibold text-orange-800">Patients Needing Attention (${data.critical_patients.length})</h4>
                                <div class="mt-2 space-y-2">
                                    ${data.critical_patients.map(patient => `
                                        <div class="text-sm">
                                            <span class="font-medium">${patient.name}</span> -
                                            <span class="text-gray-600">Last visit: ${patient.last_visit || 'Never'}</span>
                                            <a href="/api/patients/${patient.zimhealth_id}/" class="text-orange-600 hover:text-orange-700 ml-2">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Failed to load alert details:', error);
            const content = document.getElementById('alert-details-content');
            if (content) {
                content.innerHTML = '<p class="text-red-600">Failed to load alert details.</p>';
            }
        });
}

/**
 * Show emergency modal
 */
function showEmergencyModal() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div class="flex items-center space-x-3 mb-4">
                <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-bold text-gray-900">Emergency Protocol</h3>
                    <p class="text-sm text-gray-600">Quick access to emergency procedures</p>
                </div>
            </div>
            <div class="space-y-3">
                <a href="/api/patients/new/" class="block w-full bg-red-600 text-white px-4 py-3 rounded-lg hover:bg-red-700 transition-colors text-center font-medium">
                    <i class="fas fa-user-plus mr-2"></i>Register Emergency Patient
                </a>
                <a href="/api/appointments/new/" class="block w-full bg-orange-600 text-white px-4 py-3 rounded-lg hover:bg-orange-700 transition-colors text-center font-medium">
                    <i class="fas fa-calendar-plus mr-2"></i>Schedule Urgent Appointment
                </a>
                <a href="/api/medical-records/new/" class="block w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition-colors text-center font-medium">
                    <i class="fas fa-file-medical-alt mr-2"></i>Create Emergency Record
                </a>
            </div>
            <div class="mt-6 flex justify-end">
                <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                    Cancel
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

/**
 * Initialize demo activity data if no activities exist
 */
function initActivityDemo() {
    const activityContent = document.querySelector('.activity-content');
    const activityEmpty = document.querySelector('.activity-empty');

    if (activityEmpty && activityContent) {
        // Create demo activities
        const demoActivities = [
            {
                icon: 'user-plus',
                title: 'New Patient Registration',
                description: 'Sarah Johnson registered for comprehensive health screening',
                timestamp: '2 minutes ago'
            },
            {
                icon: 'calendar-check',
                title: 'Appointment Completed',
                description: 'Dr. Smith completed consultation with Michael Brown',
                timestamp: '15 minutes ago'
            },
            {
                icon: 'file-medical-alt',
                title: 'Medical Record Updated',
                description: 'Lab results added to Emma Wilson\'s patient file',
                timestamp: '32 minutes ago'
            },
            {
                icon: 'pills',
                title: 'Prescription Issued',
                description: 'Medication prescribed for David Martinez - Hypertension treatment',
                timestamp: '1 hour ago'
            },
            {
                icon: 'heartbeat',
                title: 'Vital Signs Recorded',
                description: 'Blood pressure and heart rate logged for Lisa Chen',
                timestamp: '2 hours ago'
            }
        ];

        // Replace empty state with demo activities
        activityContent.innerHTML = `
            <div class="space-y-0">
                ${demoActivities.map(activity => `
                    <div class="activity-item flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div class="activity-icon">
                                <i class="fas fa-${activity.icon} text-medical-600 text-sm"></i>
                            </div>
                        </div>
                        <div class="activity-details">
                            <p class="activity-title-text">${activity.title}</p>
                            <p class="activity-description">${activity.description}</p>
                            <p class="activity-timestamp">${activity.timestamp}</p>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        // Re-initialize activity interactions
        initActivityTimeline();
    }
}

// Add enhanced CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-fade-in-up {
        animation: fadeInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .animate-on-scroll {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .animate-on-scroll.animate-in {
        opacity: 1;
        transform: translateY(0);
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .activity-item.selected {
        transition: all 0.3s ease;
        background: linear-gradient(135deg, rgba(14, 165, 233, 0.05), rgba(34, 197, 94, 0.03));
        border-left: 3px solid #0ea5e9;
    }

    .dashboard-notification {
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
`;
document.head.appendChild(style);
