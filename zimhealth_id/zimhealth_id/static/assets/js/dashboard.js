/**
 * Professional Dashboard JavaScript for ZimHealth-ID
 * Handles interactive features and animations
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    // Initialize dashboard functionality
    initStatCardAnimations();
    initQuickActionInteractions();
    initActivityTimeline();
    
    if (!prefersReducedMotion) {
        initScrollAnimations();
        initCounterAnimations();
        initCardHoverEffects();
    }
});

/**
 * Initialize statistics card animations
 */
function initStatCardAnimations() {
    const statCards = document.querySelectorAll('.stat-card');
    
    statCards.forEach((card, index) => {
        // Stagger the initial animation
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate-fade-in-up');
        
        // Add click interaction for detailed view
        card.addEventListener('click', function() {
            const cardType = this.classList.contains('patients') ? 'patients' :
                           this.classList.contains('appointments') ? 'appointments' :
                           this.classList.contains('records') ? 'records' : 'prescriptions';
            
            showDetailedView(cardType);
        });
    });
}

/**
 * Initialize enhanced quick action interactions
 */
function initQuickActionInteractions() {
    const quickActions = document.querySelectorAll('.quick-action');

    quickActions.forEach((action, index) => {
        // Stagger initial animations
        action.style.animationDelay = `${index * 0.1}s`;
        action.classList.add('animate-fade-in-up');

        action.addEventListener('click', function(e) {
            e.preventDefault();

            // Enhanced click animation with ripple effect
            createRippleEffect(this);

            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);

            // Show action feedback with enhanced styling
            const actionText = this.querySelector('span').textContent;
            const actionType = this.classList.contains('medical') ? 'medical' :
                             this.classList.contains('health') ? 'health' :
                             this.classList.contains('blue') ? 'info' :
                             this.classList.contains('purple') ? 'info' :
                             this.classList.contains('orange') ? 'warning' :
                             this.classList.contains('red') ? 'error' : 'info';

            showNotification(`${actionText} module launching soon!`, actionType);
        });

        // Add hover sound effect (optional)
        action.addEventListener('mouseenter', function() {
            // Subtle hover feedback
            this.style.boxShadow = '0 15px 35px rgba(14, 165, 233, 0.2)';
        });

        action.addEventListener('mouseleave', function() {
            this.style.boxShadow = '';
        });
    });
}

/**
 * Create ripple effect for enhanced interactions
 */
function createRippleEffect(element) {
    const ripple = document.createElement('div');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);

    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = (rect.width / 2 - size / 2) + 'px';
    ripple.style.top = (rect.height / 2 - size / 2) + 'px';
    ripple.style.position = 'absolute';
    ripple.style.borderRadius = '50%';
    ripple.style.background = 'rgba(14, 165, 233, 0.3)';
    ripple.style.transform = 'scale(0)';
    ripple.style.animation = 'ripple 0.6s linear';
    ripple.style.pointerEvents = 'none';

    element.style.position = 'relative';
    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * Initialize activity timeline interactions
 */
function initActivityTimeline() {
    const activityItems = document.querySelectorAll('.activity-item');
    
    activityItems.forEach(item => {
        item.addEventListener('click', function() {
            // Highlight selected activity
            activityItems.forEach(i => i.classList.remove('selected'));
            this.classList.add('selected');
            
            // Add selection styling
            this.style.background = 'linear-gradient(135deg, rgba(14, 165, 233, 0.05), rgba(34, 197, 94, 0.03))';
            this.style.borderLeft = '3px solid #0ea5e9';
        });
    });
}

/**
 * Initialize scroll-triggered animations
 */
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe content cards
    document.querySelectorAll('.content-card').forEach(card => {
        card.classList.add('animate-on-scroll');
        observer.observe(card);
    });
}

/**
 * Initialize counter animations for statistics
 */
function initCounterAnimations() {
    const statNumbers = document.querySelectorAll('.stat-card .text-2xl');
    
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                counterObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });
    
    statNumbers.forEach(number => {
        counterObserver.observe(number);
    });
}

/**
 * Animate counter numbers
 */
function animateCounter(element) {
    const text = element.textContent;
    const number = parseInt(text.replace(/[^\d]/g, ''));
    const suffix = text.replace(/[\d]/g, '');
    
    if (isNaN(number)) return;
    
    const duration = 2000;
    const startTime = performance.now();
    
    function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const current = Math.floor(number * easeOutQuart(progress));
        
        if (number >= 1000) {
            element.textContent = (current / 1000).toFixed(1) + 'K' + suffix.replace(/[\d.K]/g, '');
        } else {
            element.textContent = current + suffix.replace(/[\d]/g, '');
        }
        
        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        }
    }
    
    requestAnimationFrame(updateCounter);
}

/**
 * Easing function for smooth animations
 */
function easeOutQuart(t) {
    return 1 - (--t) * t * t * t;
}

/**
 * Initialize card hover effects
 */
function initCardHoverEffects() {
    const cards = document.querySelectorAll('.stat-card, .content-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
}

/**
 * Show detailed view for statistics
 */
function showDetailedView(type) {
    const titles = {
        patients: 'Patient Management',
        appointments: 'Appointment Schedule',
        records: 'Medical Records',
        prescriptions: 'Prescription Management'
    };
    
    showNotification(`${titles[type]} detailed view coming soon!`, 'info');
}

/**
 * Enhanced notification system with multiple types
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existing = document.querySelector('.dashboard-notification');
    if (existing) existing.remove();

    const notification = document.createElement('div');
    const typeConfig = {
        medical: { bg: 'bg-medical-500', icon: 'fa-user-md' },
        health: { bg: 'bg-health-500', icon: 'fa-heartbeat' },
        success: { bg: 'bg-health-500', icon: 'fa-check-circle' },
        error: { bg: 'bg-red-500', icon: 'fa-exclamation-circle' },
        warning: { bg: 'bg-orange-500', icon: 'fa-exclamation-triangle' },
        info: { bg: 'bg-medical-500', icon: 'fa-info-circle' }
    };

    const config = typeConfig[type] || typeConfig.info;

    notification.className = `dashboard-notification fixed top-20 right-4 z-50 p-4 rounded-xl shadow-2xl max-w-sm transform translate-x-full transition-all duration-500 ${config.bg} text-white backdrop-blur-lg`;

    notification.innerHTML = `
        <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
                <i class="fas ${config.icon} text-lg"></i>
            </div>
            <div class="flex-1">
                <p class="font-semibold text-sm">${message}</p>
                <p class="text-xs opacity-90 mt-1">ZimHealth-ID System</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-1 transition-colors">
                <i class="fas fa-times text-sm"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in with enhanced effect
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
        notification.style.transform = 'translateX(0) scale(1)';
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%) scale(0.9)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 500);
    }, 5000);
}

/**
 * Real-time clock update
 */
function initRealTimeClock() {
    const clockElement = document.querySelector('.dashboard-clock');
    if (clockElement) {
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit'
            });
            clockElement.textContent = timeString;
        }
        
        updateClock();
        setInterval(updateClock, 1000);
    }
}

/**
 * Initialize search functionality
 */
function initDashboardSearch() {
    const searchInput = document.querySelector('.dashboard-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function() {
            const query = this.value.toLowerCase();
            // Implement search functionality here
            console.log('Searching for:', query);
        }, 300));
    }
}

/**
 * Utility function to debounce events
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize additional features
document.addEventListener('DOMContentLoaded', function() {
    initRealTimeClock();
    initDashboardSearch();
    initActivityDemo();
});

/**
 * Initialize demo activity data if no activities exist
 */
function initActivityDemo() {
    const activityContent = document.querySelector('.activity-content');
    const activityEmpty = document.querySelector('.activity-empty');

    if (activityEmpty && activityContent) {
        // Create demo activities
        const demoActivities = [
            {
                icon: 'user-plus',
                title: 'New Patient Registration',
                description: 'Sarah Johnson registered for comprehensive health screening',
                timestamp: '2 minutes ago'
            },
            {
                icon: 'calendar-check',
                title: 'Appointment Completed',
                description: 'Dr. Smith completed consultation with Michael Brown',
                timestamp: '15 minutes ago'
            },
            {
                icon: 'file-medical-alt',
                title: 'Medical Record Updated',
                description: 'Lab results added to Emma Wilson\'s patient file',
                timestamp: '32 minutes ago'
            },
            {
                icon: 'pills',
                title: 'Prescription Issued',
                description: 'Medication prescribed for David Martinez - Hypertension treatment',
                timestamp: '1 hour ago'
            },
            {
                icon: 'heartbeat',
                title: 'Vital Signs Recorded',
                description: 'Blood pressure and heart rate logged for Lisa Chen',
                timestamp: '2 hours ago'
            }
        ];

        // Replace empty state with demo activities
        activityContent.innerHTML = `
            <div class="space-y-0">
                ${demoActivities.map(activity => `
                    <div class="activity-item flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div class="activity-icon">
                                <i class="fas fa-${activity.icon} text-medical-600 text-sm"></i>
                            </div>
                        </div>
                        <div class="activity-details">
                            <p class="activity-title-text">${activity.title}</p>
                            <p class="activity-description">${activity.description}</p>
                            <p class="activity-timestamp">${activity.timestamp}</p>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        // Re-initialize activity interactions
        initActivityTimeline();
    }
}

// Add enhanced CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-fade-in-up {
        animation: fadeInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .animate-on-scroll {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .animate-on-scroll.animate-in {
        opacity: 1;
        transform: translateY(0);
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .activity-item.selected {
        transition: all 0.3s ease;
        background: linear-gradient(135deg, rgba(14, 165, 233, 0.05), rgba(34, 197, 94, 0.03));
        border-left: 3px solid #0ea5e9;
    }

    .dashboard-notification {
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
`;
document.head.appendChild(style);

/**
 * Patient Search Modal Functions
 */
function openPatientSearch() {
    document.getElementById('patient-search-modal').classList.remove('hidden');
    document.getElementById('quick-patient-search').focus();
}

function closePatientSearch() {
    document.getElementById('patient-search-modal').classList.add('hidden');
    document.getElementById('quick-patient-search').value = '';
    document.getElementById('search-results').innerHTML = '';
}

/**
 * Emergency Protocol Modal Functions
 */
function openEmergencyProtocol() {
    document.getElementById('emergency-modal').classList.remove('hidden');
}

function closeEmergencyProtocol() {
    document.getElementById('emergency-modal').classList.add('hidden');
}

/**
 * Quick Patient Search Functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('quick-patient-search');
    const searchResults = document.getElementById('search-results');
    
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function() {
            const query = this.value.trim();
            
            if (query.length < 2) {
                searchResults.innerHTML = '';
                return;
            }
            
            // Show loading
            searchResults.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin"></i> Searching...</div>';
            
            fetch(`/api/ajax/quick-patient-search/?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.patients.length === 0) {
                        searchResults.innerHTML = '<div class="text-center py-4 text-gray-500">No patients found</div>';
                        return;
                    }
                    
                    let html = '';
                    data.patients.forEach(patient => {
                        html += `
                            <div class="border-b border-gray-200 py-3 hover:bg-gray-50 cursor-pointer" onclick="window.location.href='${patient.url}'">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <p class="font-semibold text-gray-900">${patient.name}</p>
                                        <p class="text-sm text-gray-600">ID: ${patient.id} • Age: ${patient.age}</p>
                                        <p class="text-xs text-gray-500">Last visit: ${patient.last_visit}</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm text-gray-600">${patient.phone}</p>
                                        <i class="fas fa-chevron-right text-gray-400"></i>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    searchResults.innerHTML = html;
                })
                .catch(error => {
                    console.error('Search error:', error);
                    searchResults.innerHTML = '<div class="text-center py-4 text-red-500">Search failed. Please try again.</div>';
                });
        }, 300));
    }
    
    // Close modals on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closePatientSearch();
            closeEmergencyProtocol();
        }
    });
    
    // Close modals on outside click
    document.getElementById('patient-search-modal')?.addEventListener('click', function(e) {
        if (e.target === this) {
            closePatientSearch();
        }
    });
    
    document.getElementById('emergency-modal')?.addEventListener('click', function(e) {
        if (e.target === this) {
            closeEmergencyProtocol();
        }
    });
});

/**
 * Debounce function for search input
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
