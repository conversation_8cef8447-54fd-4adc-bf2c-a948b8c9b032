/**
 * Professional Prescriptions Page JavaScript for ZimHealth-ID
 * Handles search, filtering, QR scanning, and interactive features
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize prescriptions page functionality
    initPrescriptionsSearch();
    initQRCodeScanner();
    initFilterFunctionality();
    initActionButtons();
    initScrollAnimations();
    initDemoData();
});

/**
 * Initialize enhanced prescriptions search functionality
 */
function initPrescriptionsSearch() {
    const searchInput = document.getElementById('prescription-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            const searchTerm = e.target.value.toLowerCase();
            filterPrescriptions(searchTerm);
        }, 300));
    }
}

/**
 * Initialize QR Code Scanner functionality
 */
function initQRCodeScanner() {
    const qrButton = document.getElementById('prescription-qr-scanner-btn');
    if (qrButton) {
        qrButton.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
            
            // Check if browser supports camera access
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                startQRScanner();
            } else {
                showNotification('Camera access not supported in this browser', 'error');
            }
        });
    }
}

/**
 * Start QR Code Scanner
 */
function startQRScanner() {
    showNotification('QR Scanner starting...', 'info');
    
    // Simulate QR scanning process
    setTimeout(() => {
        // For demo purposes, simulate a successful scan
        const demoPatientId = 'ZH-2024-001';
        const searchInput = document.getElementById('prescription-search');
        
        if (searchInput) {
            searchInput.value = demoPatientId;
            filterPrescriptions(demoPatientId.toLowerCase());
            showNotification(`QR Code scanned successfully! Found prescriptions for ${demoPatientId}`, 'success');
        }
    }, 2000);
}

/**
 * Filter prescriptions based on search term
 */
function filterPrescriptions(searchTerm) {
    const prescriptions = document.querySelectorAll('.prescriptions-table tbody tr:not(.details-row)');
    let visibleCount = 0;
    
    prescriptions.forEach(prescription => {
        const text = prescription.textContent.toLowerCase();
        const isVisible = text.includes(searchTerm);
        
        // Handle details row visibility
        const nextRow = prescription.nextElementSibling;
        const isDetailsRow = nextRow && nextRow.classList.contains('details-row');
        
        if (isVisible) {
            prescription.style.display = '';
            if (isDetailsRow) nextRow.style.display = '';
            visibleCount++;
        } else {
            prescription.style.display = 'none';
            if (isDetailsRow) nextRow.style.display = 'none';
        }
    });
    
    updateResultsCount(visibleCount);
}

/**
 * Initialize filter functionality
 */
function initFilterFunctionality() {
    const statusFilter = document.getElementById('status-filter');
    const frequencyFilter = document.getElementById('frequency-filter');
    const dateFilter = document.getElementById('date-filter');
    
    if (statusFilter) statusFilter.addEventListener('change', applyFilters);
    if (frequencyFilter) frequencyFilter.addEventListener('change', applyFilters);
    if (dateFilter) dateFilter.addEventListener('change', applyFilters);
}

/**
 * Apply filters to prescriptions list
 */
function applyFilters() {
    const statusFilter = document.getElementById('status-filter')?.value || '';
    const frequencyFilter = document.getElementById('frequency-filter')?.value || '';
    const dateFilter = document.getElementById('date-filter')?.value || '';
    const searchTerm = document.getElementById('prescription-search')?.value.toLowerCase() || '';
    
    const prescriptions = document.querySelectorAll('.prescriptions-table tbody tr:not(.details-row)');
    let visibleCount = 0;
    
    prescriptions.forEach(prescription => {
        const prescriptionText = prescription.textContent.toLowerCase();
        const statusText = prescription.querySelector('.prescription-status-badge')?.textContent.toLowerCase() || '';
        const frequencyText = prescription.querySelector('.frequency-badge')?.textContent.toLowerCase() || '';
        const dateText = prescription.querySelector('.prescription-date')?.textContent || '';
        
        const matchesSearch = !searchTerm || prescriptionText.includes(searchTerm);
        const matchesStatus = !statusFilter || statusText.includes(statusFilter.toLowerCase());
        const matchesFrequency = !frequencyFilter || frequencyText.includes(frequencyFilter.toLowerCase());
        const matchesDate = !dateFilter || dateText.includes(dateFilter);
        
        const isVisible = matchesSearch && matchesStatus && matchesFrequency && matchesDate;
        
        // Handle details row visibility
        const nextRow = prescription.nextElementSibling;
        const isDetailsRow = nextRow && nextRow.classList.contains('details-row');
        
        if (isVisible) {
            prescription.style.display = '';
            if (isDetailsRow) nextRow.style.display = '';
            visibleCount++;
        } else {
            prescription.style.display = 'none';
            if (isDetailsRow) nextRow.style.display = 'none';
        }
    });
    
    updateResultsCount(visibleCount);
}

/**
 * Initialize action buttons
 */
function initActionButtons() {
    const actionButtons = document.querySelectorAll('.prescription-action-button');
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
            
            // Handle different actions
            const action = this.getAttribute('data-action');
            const prescriptionId = this.getAttribute('data-prescription-id');
            
            switch(action) {
                case 'view':
                    handleViewPrescription(prescriptionId);
                    break;
                case 'complete':
                    handleCompletePrescription(prescriptionId);
                    break;
                case 'hold':
                    handleHoldPrescription(prescriptionId);
                    break;
                case 'discontinue':
                    handleDiscontinuePrescription(prescriptionId);
                    break;
                case 'print':
                    handlePrintPrescription(prescriptionId);
                    break;
                case 'edit':
                    handleEditPrescription(prescriptionId);
                    break;
            }
        });
    });
}

/**
 * Handle prescription actions
 */
function handleViewPrescription(prescriptionId) {
    showNotification('Opening prescription details', 'info');
}

function handleCompletePrescription(prescriptionId) {
    showNotification('Prescription marked as completed', 'success');
}

function handleHoldPrescription(prescriptionId) {
    showNotification('Prescription put on hold', 'warning');
}

function handleDiscontinuePrescription(prescriptionId) {
    if (confirm('Are you sure you want to discontinue this prescription?')) {
        showNotification('Prescription discontinued', 'warning');
    }
}

function handlePrintPrescription(prescriptionId) {
    showNotification('Preparing prescription for printing', 'info');
}

function handleEditPrescription(prescriptionId) {
    showNotification('Opening prescription editor', 'info');
}

/**
 * Update results count display
 */
function updateResultsCount(count) {
    const countElement = document.querySelector('.results-count');
    if (countElement) {
        countElement.textContent = `${count} prescriptions`;
    }
}

/**
 * Initialize scroll animations
 */
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe cards and table elements
    document.querySelectorAll('.stats-card, .prescriptions-table-card').forEach(element => {
        element.classList.add('animate-on-scroll');
        observer.observe(element);
    });
}

/**
 * Initialize demo data if no prescriptions exist
 */
function initDemoData() {
    const emptyState = document.querySelector('td[colspan="7"]');
    const prescriptionsTBody = document.querySelector('.prescriptions-table tbody');
    
    if (emptyState && prescriptionsTBody) {
        // Create demo prescriptions
        const demoPrescriptions = [
            {
                id: 'RX-001',
                medication: 'Metformin 500mg',
                patientName: 'Sarah Johnson',
                patientId: 'ZH-2024-001',
                dosage: '500mg',
                frequency: 'Twice Daily',
                status: 'active',
                doctor: 'Dr. Smith',
                facility: 'Central Hospital',
                startDate: 'Jul 15, 2025',
                instructions: 'Take with meals to reduce stomach upset'
            },
            {
                id: 'RX-002',
                medication: 'Lisinopril 10mg',
                patientName: 'Michael Brown',
                patientId: 'ZH-2024-002',
                dosage: '10mg',
                frequency: 'Once Daily',
                status: 'completed',
                doctor: 'Dr. Wilson',
                facility: 'Medical Center',
                startDate: 'Jul 10, 2025',
                instructions: 'Take in the morning, monitor blood pressure'
            },
            {
                id: 'RX-003',
                medication: 'Amoxicillin 250mg',
                patientName: 'Emma Wilson',
                patientId: 'ZH-2024-003',
                dosage: '250mg',
                frequency: 'Three Times Daily',
                status: 'on_hold',
                doctor: 'Dr. Martinez',
                facility: 'Health Clinic',
                startDate: 'Jul 20, 2025',
                instructions: 'Complete full course even if feeling better'
            }
        ];
        
        // Replace empty state with demo prescriptions
        prescriptionsTBody.innerHTML = demoPrescriptions.map(prescription => 
            createPrescriptionTableRow(prescription)
        ).join('');
        
        // Update results count
        updateResultsCount(demoPrescriptions.length);
        
        // Re-initialize interactions
        initActionButtons();
    }
}

/**
 * Create prescription table row HTML
 */
function createPrescriptionTableRow(prescription) {
    return `
        <tr>
            <!-- Medication Column -->
            <td class="medication-column">
                <div class="flex items-center space-x-3">
                    <div class="prescription-status-indicator ${prescription.status}"></div>
                    <div class="prescription-avatar">
                        <i class="fas fa-pills"></i>
                    </div>
                    <div>
                        <div class="text-sm font-semibold text-gray-900">${prescription.medication}</div>
                        <div class="text-xs text-gray-500">ID: ${prescription.id}</div>
                    </div>
                </div>
            </td>

            <!-- Patient Column -->
            <td class="patient-column">
                <div class="text-sm font-medium text-gray-900">${prescription.patientName}</div>
                <div class="text-xs text-gray-500">${prescription.patientId}</div>
            </td>

            <!-- Dosage Column -->
            <td class="dosage-column">
                <div class="text-sm font-medium text-gray-900">${prescription.dosage}</div>
            </td>

            <!-- Frequency Column -->
            <td class="frequency-column">
                <span class="frequency-badge">${prescription.frequency}</span>
            </td>

            <!-- Status Column -->
            <td class="status-column">
                <span class="prescription-status-badge ${prescription.status}">${prescription.status.charAt(0).toUpperCase() + prescription.status.slice(1).replace('_', ' ')}</span>
            </td>

            <!-- Doctor Column -->
            <td class="doctor-column">
                <div class="text-sm font-medium text-gray-900">${prescription.doctor}</div>
                <div class="text-xs text-gray-500">${prescription.facility}</div>
            </td>

            <!-- Actions Column -->
            <td class="actions-column">
                <div class="flex items-center justify-center space-x-1">
                    <button class="prescription-action-button view" data-action="view" data-prescription-id="${prescription.id}" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${prescription.status === 'active' ? `
                        <button class="prescription-action-button complete" data-action="complete" data-prescription-id="${prescription.id}" title="Mark Complete">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="prescription-action-button hold" data-action="hold" data-prescription-id="${prescription.id}" title="Put on Hold">
                            <i class="fas fa-clock"></i>
                        </button>
                        <button class="prescription-action-button discontinue" data-action="discontinue" data-prescription-id="${prescription.id}" title="Discontinue">
                            <i class="fas fa-ban"></i>
                        </button>
                    ` : ''}
                    <button class="prescription-action-button print" data-action="print" data-prescription-id="${prescription.id}" title="Print">
                        <i class="fas fa-print"></i>
                    </button>
                    <button class="prescription-action-button edit" data-action="edit" data-prescription-id="${prescription.id}" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </td>
        </tr>
        ${prescription.instructions ? `
            <tr class="details-row">
                <td colspan="7" class="px-6 py-3 bg-gray-50 border-b border-gray-200">
                    <p class="text-xs text-gray-600"><strong>Instructions:</strong> ${prescription.instructions}</p>
                    <p class="text-xs text-gray-600 mt-1"><strong>Start Date:</strong> ${prescription.startDate}</p>
                </td>
            </tr>
        ` : ''}
    `;
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existing = document.querySelector('.prescriptions-notification');
    if (existing) existing.remove();
    
    const notification = document.createElement('div');
    const typeConfig = {
        success: { bg: 'bg-health-500', icon: 'fa-check-circle' },
        error: { bg: 'bg-red-500', icon: 'fa-exclamation-circle' },
        warning: { bg: 'bg-orange-500', icon: 'fa-exclamation-triangle' },
        info: { bg: 'bg-medical-500', icon: 'fa-info-circle' }
    };
    
    const config = typeConfig[type] || typeConfig.info;
    
    notification.className = `prescriptions-notification fixed top-20 right-4 z-50 p-4 rounded-xl shadow-2xl max-w-sm transform translate-x-full transition-all duration-500 ${config.bg} text-white backdrop-blur-lg`;
    
    notification.innerHTML = `
        <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
                <i class="fas ${config.icon} text-lg"></i>
            </div>
            <div class="flex-1">
                <p class="font-semibold text-sm">${message}</p>
                <p class="text-xs opacity-90 mt-1">ZimHealth-ID Prescriptions</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-1 transition-colors">
                <i class="fas fa-times text-sm"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%) scale(0.9)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 500);
    }, 5000);
}

/**
 * Utility function to debounce events
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
    }
    
    .animate-on-scroll.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
`;
document.head.appendChild(style);
