/**
 * Professional Medical Records Page JavaScript for ZimHealth-ID
 * Handles search, filtering, QR scanning, and interactive features
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize medical records page functionality
    initMedicalRecordsSearch();
    initQRCodeScanner();
    initFilterFunctionality();
    initActionButtons();
    initScrollAnimations();
    initDemoData();
});

/**
 * Initialize enhanced medical records search functionality
 */
function initMedicalRecordsSearch() {
    const searchInput = document.getElementById('medical-record-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            const searchTerm = e.target.value.toLowerCase();
            filterMedicalRecords(searchTerm);
        }, 300));
    }
}

/**
 * Initialize QR Code Scanner functionality
 */
function initQRCodeScanner() {
    const qrButton = document.getElementById('medical-record-qr-scanner-btn');
    if (qrButton) {
        qrButton.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
            
            // Check if browser supports camera access
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                startQRScanner();
            } else {
                showNotification('Camera access not supported in this browser', 'error');
            }
        });
    }
}

/**
 * Start QR Code Scanner
 */
function startQRScanner() {
    showNotification('QR Scanner starting...', 'info');
    
    // Simulate QR scanning process
    setTimeout(() => {
        // For demo purposes, simulate a successful scan
        const demoPatientId = 'ZH-2024-001';
        const searchInput = document.getElementById('medical-record-search');
        
        if (searchInput) {
            searchInput.value = demoPatientId;
            filterMedicalRecords(demoPatientId.toLowerCase());
            showNotification(`QR Code scanned successfully! Found medical records for ${demoPatientId}`, 'success');
        }
    }, 2000);
}

/**
 * Filter medical records based on search term
 */
function filterMedicalRecords(searchTerm) {
    const records = document.querySelectorAll('.medical-records-table tbody tr:not(.details-row)');
    let visibleCount = 0;
    
    records.forEach(record => {
        const text = record.textContent.toLowerCase();
        const isVisible = text.includes(searchTerm);
        
        // Handle details row visibility
        const nextRow = record.nextElementSibling;
        const isDetailsRow = nextRow && nextRow.classList.contains('details-row');
        
        if (isVisible) {
            record.style.display = '';
            if (isDetailsRow) nextRow.style.display = '';
            visibleCount++;
        } else {
            record.style.display = 'none';
            if (isDetailsRow) nextRow.style.display = 'none';
        }
    });
    
    updateResultsCount(visibleCount);
}

/**
 * Initialize filter functionality
 */
function initFilterFunctionality() {
    const typeFilter = document.getElementById('type-filter');
    const statusFilter = document.getElementById('status-filter');
    const facilityFilter = document.getElementById('facility-filter');
    const dateFromFilter = document.getElementById('date-from-filter');
    const dateToFilter = document.getElementById('date-to-filter');
    
    if (typeFilter) typeFilter.addEventListener('change', applyFilters);
    if (statusFilter) statusFilter.addEventListener('change', applyFilters);
    if (facilityFilter) facilityFilter.addEventListener('change', applyFilters);
    if (dateFromFilter) dateFromFilter.addEventListener('change', applyFilters);
    if (dateToFilter) dateToFilter.addEventListener('change', applyFilters);
}

/**
 * Apply filters to medical records list
 */
function applyFilters() {
    const typeFilter = document.getElementById('type-filter')?.value || '';
    const statusFilter = document.getElementById('status-filter')?.value || '';
    const facilityFilter = document.getElementById('facility-filter')?.value || '';
    const searchTerm = document.getElementById('medical-record-search')?.value.toLowerCase() || '';
    
    const records = document.querySelectorAll('.medical-records-table tbody tr:not(.details-row)');
    let visibleCount = 0;
    
    records.forEach(record => {
        const recordText = record.textContent.toLowerCase();
        const statusText = record.querySelector('.record-status-badge')?.textContent.toLowerCase() || '';
        const typeText = record.querySelector('.record-type-badge')?.textContent.toLowerCase() || '';
        const facilityText = record.querySelector('.facility-text')?.textContent.toLowerCase() || '';
        
        const matchesSearch = !searchTerm || recordText.includes(searchTerm);
        const matchesStatus = !statusFilter || statusText.includes(statusFilter.toLowerCase());
        const matchesType = !typeFilter || typeText.includes(typeFilter.toLowerCase());
        const matchesFacility = !facilityFilter || facilityText.includes(facilityFilter.toLowerCase());
        
        const isVisible = matchesSearch && matchesStatus && matchesType && matchesFacility;
        
        // Handle details row visibility
        const nextRow = record.nextElementSibling;
        const isDetailsRow = nextRow && nextRow.classList.contains('details-row');
        
        if (isVisible) {
            record.style.display = '';
            if (isDetailsRow) nextRow.style.display = '';
            visibleCount++;
        } else {
            record.style.display = 'none';
            if (isDetailsRow) nextRow.style.display = 'none';
        }
    });
    
    updateResultsCount(visibleCount);
}

/**
 * Initialize action buttons
 */
function initActionButtons() {
    const actionButtons = document.querySelectorAll('.medical-record-action-button');
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
            
            // Handle different actions
            const action = this.getAttribute('data-action');
            const recordId = this.getAttribute('data-record-id');
            
            switch(action) {
                case 'view':
                    handleViewRecord(recordId);
                    break;
                case 'edit':
                    handleEditRecord(recordId);
                    break;
                case 'download':
                    handleDownloadRecord(recordId);
                    break;
                case 'prescription':
                    handleAddPrescription(recordId);
                    break;
                case 'print':
                    handlePrintRecord(recordId);
                    break;
            }
        });
    });
}

/**
 * Handle medical record actions
 */
function handleViewRecord(recordId) {
    showNotification('Opening medical record details', 'info');
}

function handleEditRecord(recordId) {
    showNotification('Opening medical record editor', 'info');
}

function handleDownloadRecord(recordId) {
    showNotification('Downloading medical record PDF', 'success');
}

function handleAddPrescription(recordId) {
    showNotification('Opening prescription form', 'info');
}

function handlePrintRecord(recordId) {
    showNotification('Preparing record for printing', 'info');
}

/**
 * Update results count display
 */
function updateResultsCount(count) {
    const countElement = document.querySelector('.results-count');
    if (countElement) {
        countElement.textContent = `${count} medical records`;
    }
}

/**
 * Initialize scroll animations
 */
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe cards and table elements
    document.querySelectorAll('.stats-card, .medical-records-table-card').forEach(element => {
        element.classList.add('animate-on-scroll');
        observer.observe(element);
    });
}

/**
 * Initialize demo data if no medical records exist
 */
function initDemoData() {
    const emptyState = document.querySelector('td[colspan="7"]');
    const recordsTBody = document.querySelector('.medical-records-table tbody');
    
    if (emptyState && recordsTBody) {
        // Create demo medical records
        const demoRecords = [
            {
                id: 'MR-001',
                patientName: 'Sarah Johnson',
                patientId: 'ZH-2024-001',
                date: 'Jul 28, 2025',
                time: '14:30',
                doctor: 'Dr. Smith',
                facility: 'Central Hospital',
                type: 'Consultation',
                diagnosis: 'Hypertension, Type 2 Diabetes',
                status: 'active',
                priority: 'normal'
            },
            {
                id: 'MR-002',
                patientName: 'Michael Brown',
                patientId: 'ZH-2024-002',
                date: 'Jul 27, 2025',
                time: '10:15',
                doctor: 'Dr. Wilson',
                facility: 'Medical Center',
                type: 'Follow-up',
                diagnosis: 'Post-surgical recovery monitoring',
                status: 'pending',
                priority: 'high'
            },
            {
                id: 'MR-003',
                patientName: 'Emma Wilson',
                patientId: 'ZH-2024-003',
                date: 'Jul 26, 2025',
                time: '16:45',
                doctor: 'Dr. Martinez',
                facility: 'Health Clinic',
                type: 'Emergency',
                diagnosis: 'Acute respiratory infection',
                status: 'archived',
                priority: 'urgent'
            }
        ];
        
        // Replace empty state with demo records
        recordsTBody.innerHTML = demoRecords.map(record => 
            createMedicalRecordTableRow(record)
        ).join('');
        
        // Update results count
        updateResultsCount(demoRecords.length);
        
        // Re-initialize interactions
        initActionButtons();
    }
}

/**
 * Create medical record table row HTML
 */
function createMedicalRecordTableRow(record) {
    return `
        <tr>
            <!-- Patient Column -->
            <td class="patient-column">
                <div class="flex items-center space-x-3">
                    <div class="record-status-indicator ${record.status}"></div>
                    <div class="medical-record-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <div class="text-sm font-semibold text-gray-900">${record.patientName}</div>
                        <div class="text-xs text-gray-500">${record.patientId}</div>
                    </div>
                </div>
            </td>

            <!-- Date Column -->
            <td class="date-column">
                <div class="text-sm font-medium text-gray-900">${record.date}</div>
                <div class="text-xs text-gray-500">${record.time}</div>
            </td>

            <!-- Healthcare Provider Column -->
            <td class="provider-column">
                <div class="text-sm font-medium text-gray-900">${record.doctor}</div>
                <div class="text-xs text-gray-500 facility-text">${record.facility}</div>
            </td>

            <!-- Type Column -->
            <td class="type-column">
                <span class="record-type-badge">${record.type}</span>
                ${record.priority !== 'normal' ? `<div class="priority-indicator ${record.priority} mt-1 text-xs">${record.priority.toUpperCase()}</div>` : ''}
            </td>

            <!-- Diagnosis Column -->
            <td class="diagnosis-column">
                <div class="text-sm text-gray-900" title="${record.diagnosis}">${record.diagnosis.length > 40 ? record.diagnosis.substring(0, 40) + '...' : record.diagnosis}</div>
            </td>

            <!-- Status Column -->
            <td class="status-column">
                <span class="record-status-badge ${record.status}">${record.status.charAt(0).toUpperCase() + record.status.slice(1)}</span>
            </td>

            <!-- Actions Column -->
            <td class="actions-column">
                <div class="flex items-center justify-center space-x-1">
                    <button class="medical-record-action-button view" data-action="view" data-record-id="${record.id}" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="medical-record-action-button edit" data-action="edit" data-record-id="${record.id}" title="Edit Record">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="medical-record-action-button download" data-action="download" data-record-id="${record.id}" title="Download PDF">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="medical-record-action-button prescription" data-action="prescription" data-record-id="${record.id}" title="Add Prescription">
                        <i class="fas fa-pills"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existing = document.querySelector('.medical-records-notification');
    if (existing) existing.remove();
    
    const notification = document.createElement('div');
    const typeConfig = {
        success: { bg: 'bg-health-500', icon: 'fa-check-circle' },
        error: { bg: 'bg-red-500', icon: 'fa-exclamation-circle' },
        warning: { bg: 'bg-orange-500', icon: 'fa-exclamation-triangle' },
        info: { bg: 'bg-medical-500', icon: 'fa-info-circle' }
    };
    
    const config = typeConfig[type] || typeConfig.info;
    
    notification.className = `medical-records-notification fixed top-20 right-4 z-50 p-4 rounded-xl shadow-2xl max-w-sm transform translate-x-full transition-all duration-500 ${config.bg} text-white backdrop-blur-lg`;
    
    notification.innerHTML = `
        <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
                <i class="fas ${config.icon} text-lg"></i>
            </div>
            <div class="flex-1">
                <p class="font-semibold text-sm">${message}</p>
                <p class="text-xs opacity-90 mt-1">ZimHealth-ID Medical Records</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-1 transition-colors">
                <i class="fas fa-times text-sm"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%) scale(0.9)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 500);
    }, 5000);
}

/**
 * Utility function to debounce events
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
    }
    
    .animate-on-scroll.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
`;
document.head.appendChild(style);
