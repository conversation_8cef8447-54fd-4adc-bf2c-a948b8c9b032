/* Professional Appointments Page Styles for ZimHealth-ID */

/* Government-grade appointments layout */
.appointments-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    position: relative;
}

/* Sophisticated background pattern */
.appointments-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.02) 0%, transparent 50%);
    background-size: 1200px 1200px, 800px 800px;
    pointer-events: none;
    z-index: 0;
}

/* Enhanced appointments header styling */
.appointments-header {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.2);
    position: relative;
    z-index: 10;
}

/* Professional stats cards */
.stats-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    box-shadow: 
        0 8px 25px rgba(14, 165, 233, 0.06),
        0 4px 12px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 12px 30px rgba(14, 165, 233, 0.08),
        0 6px 16px rgba(0, 0, 0, 0.06);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.stats-icon.today {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.05));
    color: #22c55e;
}

.stats-icon.pending {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.15), rgba(14, 165, 233, 0.05));
    color: #0ea5e9;
}

.stats-icon.completed {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.05));
    color: #22c55e;
}

.stats-icon.cancelled {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.05));
    color: #ef4444;
}

/* Government-Level Professional Search Controls */
.appointments-search-input {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.875rem;
    color: #374151;
    transition: border-color 0.2s ease;
}

.appointments-search-input:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 1px #6b7280;
}

.appointments-filter-button {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
}

.appointments-filter-button:hover {
    border-color: #9ca3af;
    background: #f9fafb;
}

.appointments-filter-button:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 1px #6b7280;
}

/* QR Scanner Button inside Appointments Search Bar */
.appointment-search-qr-button {
    color: #6b7280;
    cursor: pointer;
    transition: color 0.2s ease;
    padding: 0.5rem;
    border-radius: 3px;
}

.appointment-search-qr-button:hover {
    color: #374151;
    background: rgba(243, 244, 246, 0.5);
}

.appointment-search-qr-button:focus {
    outline: none;
    color: #1f2937;
    background: rgba(243, 244, 246, 0.8);
}

/* Professional appointments table */
.appointments-table-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.appointments-table-header {
    background: #f9fafb;
    border-bottom: 2px solid #e5e7eb;
    padding: 1rem 1.5rem;
}

.appointments-table-header h3 {
    color: #111827;
    font-weight: 600;
    font-size: 1rem;
    margin: 0;
}

/* Professional appointment table layout */
.appointments-table {
    width: 100%;
    border-collapse: collapse;
    background: #ffffff;
}

.appointments-table thead {
    background: #f3f4f6;
    border-bottom: 2px solid #e5e7eb;
}

.appointments-table th {
    padding: 1rem 1.25rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    border-right: 1px solid #f3f4f6;
    white-space: nowrap;
}

.appointments-table th:last-child {
    border-right: none;
}

.appointments-table tbody tr {
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    transition: background-color 0.2s ease;
}

.appointments-table tbody tr:nth-child(even) {
    background: #fafafa;
}

.appointments-table tbody tr:hover {
    background: #f3f4f6;
}

.appointments-table td {
    padding: 1rem 1.25rem;
    border-right: 1px solid #f9fafb;
    color: #374151;
    font-size: 0.875rem;
    vertical-align: middle;
}

.appointments-table td:last-child {
    border-right: none;
}

/* Column-specific styling for proper alignment */
.appointments-table .patient-column {
    min-width: 200px;
    max-width: 250px;
}

.appointments-table .datetime-column {
    min-width: 120px;
    text-align: center;
    white-space: nowrap;
}

.appointments-table .doctor-column {
    min-width: 150px;
    text-align: center;
}

.appointments-table .type-column {
    min-width: 120px;
    text-align: center;
}

.appointments-table .status-column {
    min-width: 100px;
    text-align: center;
}

.appointments-table .actions-column {
    min-width: 180px;
    text-align: center;
}

/* Notes row styling */
.appointments-table .notes-row {
    background: #f9fafb !important;
}

.appointments-table .notes-row:hover {
    background: #f3f4f6 !important;
}

.appointments-table .notes-row td {
    border-top: none;
    font-size: 0.75rem;
    color: #6b7280;
}

/* Enhanced table borders and spacing */
.appointments-table {
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.appointments-table thead th:first-child {
    border-top-left-radius: 4px;
}

.appointments-table thead th:last-child {
    border-top-right-radius: 4px;
}

/* Professional table hover effects */
.appointments-table tbody tr:not(.notes-row):hover {
    background: #f8fafc !important;
    box-shadow: inset 0 0 0 1px rgba(14, 165, 233, 0.1);
}

/* Enhanced status indicator positioning */
.status-indicator {
    flex-shrink: 0;
}

/* Professional table text alignment */
.appointments-table .patient-column {
    text-align: left;
}

.appointments-table .datetime-column,
.appointments-table .doctor-column,
.appointments-table .type-column,
.appointments-table .status-column,
.appointments-table .actions-column {
    text-align: center;
}

/* Responsive table adjustments */
@media (max-width: 1024px) {
    .appointments-table .doctor-column,
    .appointments-table .type-column {
        min-width: 100px;
    }

    .appointments-table .actions-column {
        min-width: 140px;
    }
}

@media (max-width: 768px) {
    .appointments-table {
        font-size: 0.8rem;
    }

    .appointments-table th,
    .appointments-table td {
        padding: 0.75rem 0.5rem;
    }

    .appointments-table .patient-column {
        min-width: 160px;
    }

    .appointments-table .datetime-column {
        min-width: 100px;
    }

    .appointments-table .actions-column {
        min-width: 120px;
    }
}

/* Government-Level Professional Status Indicators */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-indicator.scheduled {
    background: #0ea5e9;
}

.status-indicator.completed {
    background: #22c55e;
}

.status-indicator.cancelled {
    background: #ef4444;
}

.status-indicator.no-show {
    background: #f59e0b;
}

/* Professional patient avatar */
.appointment-avatar {
    width: 48px;
    height: 48px;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.appointment-avatar i {
    color: #6b7280;
    font-size: 1.125rem;
}

/* Government-Level Professional Action Buttons */
.appointment-action-button {
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    color: #6b7280;
    font-size: 0.875rem;
}

.appointment-action-button:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
}

.appointment-action-button.complete {
    color: #22c55e;
}

.appointment-action-button.complete:hover {
    background: rgba(34, 197, 94, 0.05);
    border-color: rgba(34, 197, 94, 0.2);
}

.appointment-action-button.reschedule {
    color: #0ea5e9;
}

.appointment-action-button.reschedule:hover {
    background: rgba(14, 165, 233, 0.05);
    border-color: rgba(14, 165, 233, 0.2);
}

.appointment-action-button.cancel {
    color: #ef4444;
}

.appointment-action-button.cancel:hover {
    background: rgba(239, 68, 68, 0.05);
    border-color: rgba(239, 68, 68, 0.2);
}

/* Professional status badges */
.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
    font-size: 0.75rem;
    display: inline-block;
    border: 1px solid;
}

.status-badge.scheduled {
    background: rgba(14, 165, 233, 0.1);
    border-color: rgba(14, 165, 233, 0.2);
    color: #0ea5e9;
}

.status-badge.completed {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.status-badge.cancelled {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.status-badge.no-show {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

/* Professional type badges */
.type-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.75rem;
    display: inline-block;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    color: #374151;
}

/* Enhanced Schedule Appointment Button */
.schedule-appointment-button {
    background: linear-gradient(135deg, #1e40af, #1d4ed8);
    border: 1px solid #1e3a8a;
    border-radius: 6px;
    color: white;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 
        0 2px 4px rgba(0, 0, 0, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.schedule-appointment-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.schedule-appointment-button:hover {
    background: linear-gradient(135deg, #1d4ed8, #2563eb);
    border-color: #1e40af;
    transform: translateY(-1px);
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1);
}

.schedule-appointment-button:hover::before {
    left: 100%;
}

/* Enhanced pagination */
.pagination-button {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    transition: all 0.2s ease;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    color: #374151;
}

.pagination-button:hover {
    background: #f9fafb;
    border-color: #9ca3af;
}

/* Government-Level Responsive Design */
@media (max-width: 768px) {
    .appointments-table-card {
        border-radius: 4px;
        margin: 0 0.5rem;
    }
    
    .appointment-card {
        padding: 1rem;
    }
    
    .appointment-avatar {
        width: 40px;
        height: 40px;
    }
    
    .appointment-action-button {
        width: 32px;
        height: 32px;
        font-size: 0.75rem;
    }
    
    .appointments-filter-button {
        min-width: 100px;
        font-size: 0.75rem;
    }
    
    .appointments-search-input {
        font-size: 0.8rem;
    }
}

@media (max-width: 640px) {
    .stats-card {
        border-radius: 8px;
    }
    
    .stats-icon {
        width: 40px;
        height: 40px;
    }
}

/* View toggle buttons */
.view-toggle-button {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid #d1d5db;
    background: #ffffff;
    color: #6b7280;
}

.view-toggle-button.active {
    background: #1e40af;
    border-color: #1e3a8a;
    color: white;
}

.view-toggle-button:hover:not(.active) {
    background: #f9fafb;
    border-color: #9ca3af;
}

/* Professional priority indicators */
.priority-indicator {
    font-size: 0.75rem;
    font-weight: 600;
    color: #ef4444;
}

.priority-indicator.high {
    color: #dc2626;
}

.priority-indicator.urgent {
    color: #991b1b;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
