/* Professional Patients Page Styles for ZimHealth-ID */

/* Government-grade patients layout */
.patients-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    position: relative;
}

/* Sophisticated background pattern */
.patients-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.02) 0%, transparent 50%);
    background-size: 1200px 1200px, 800px 800px;
    pointer-events: none;
    z-index: 0;
}

/* Enhanced patients header styling */
.patients-header {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.2);
    position: relative;
    z-index: 10;
}

/* Government-Level Professional Search Controls */
.government-search-input {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.875rem;
    color: #374151;
    transition: border-color 0.2s ease;
}

.government-search-input:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 1px #6b7280;
}

.government-filter-button {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
}

.government-filter-button:hover {
    border-color: #9ca3af;
    background: #f9fafb;
}

.government-filter-button:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 1px #6b7280;
}

.government-qr-button {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 0.5rem;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.government-qr-button:hover {
    border-color: #9ca3af;
    background: #f9fafb;
    color: #374151;
}

.government-qr-button:focus {
    outline: none;
    border-color: #6b7280;
    box-shadow: 0 0 0 1px #6b7280;
}

/* QR Scanner Button inside Search Bar */
.search-qr-button {
    color: #6b7280;
    cursor: pointer;
    transition: color 0.2s ease;
    padding: 0.5rem;
    border-radius: 3px;
}

.search-qr-button:hover {
    color: #374151;
    background: rgba(243, 244, 246, 0.5);
}

.search-qr-button:focus {
    outline: none;
    color: #1f2937;
    background: rgba(243, 244, 246, 0.8);
}

/* Government-Level Professional Patients Table */
.patients-table-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.patients-table-header {
    background: #f9fafb;
    border-bottom: 2px solid #e5e7eb;
    padding: 1rem 1.5rem;
}

.patients-table-header h3 {
    color: #111827;
    font-weight: 600;
    font-size: 1rem;
    margin: 0;
}

.patients-table {
    background: #ffffff;
    width: 100%;
    border-collapse: collapse;
}

.patients-table thead {
    background: #f3f4f6;
}

.patients-table th {
    font-weight: 600;
    color: #374151;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    padding: 0.875rem 1.25rem;
    border-bottom: 2px solid #e5e7eb;
    text-align: left;
    border-right: 1px solid #f3f4f6;
}

.patients-table th:last-child {
    border-right: none;
}

.patients-table td {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid #e5e7eb;
    border-right: 1px solid #f9fafb;
    color: #374151;
    font-size: 0.875rem;
}

.patients-table td:last-child {
    border-right: none;
}

.patients-table tbody tr {
    background: #ffffff;
}

.patients-table tbody tr:nth-child(even) {
    background: #fafafa;
}

.patients-table tbody tr:hover {
    background: #f3f4f6;
}

/* Government-Level Professional Patient Avatar */
.patient-avatar {
    width: 36px;
    height: 36px;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.patient-avatar i {
    color: #6b7280;
    font-size: 0.875rem;
}

/* Government-Level Professional Action Buttons */
.action-button {
    padding: 0.5rem;
    border-radius: 3px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid #d1d5db;
    background: #ffffff;
    color: #6b7280;
    font-size: 0.75rem;
}

.action-button:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
}

.action-button.view {
    color: #4b5563;
}

.action-button.view:hover {
    color: #1f2937;
}

.action-button.edit {
    color: #4b5563;
}

.action-button.edit:hover {
    color: #1f2937;
}

.action-button.medical {
    color: #4b5563;
}

.action-button.medical:hover {
    color: #1f2937;
}

.action-button.appointment {
    color: #4b5563;
}

.action-button.appointment:hover {
    color: #1f2937;
}

/* Government-Level Professional Blood Type Badge with Color Coding */
.blood-type-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-weight: 600;
    font-size: 0.75rem;
    display: inline-block;
    font-family: monospace;
    border: 1px solid;
}

/* Blood Type Color Coding - Professional Healthcare Colors */
.blood-type-badge.a-positive {
    background: #fef2f2;
    border-color: #fecaca;
    color: #7f1d1d;
}

.blood-type-badge.a-negative {
    background: #fef1f1;
    border-color: #fca5a5;
    color: #991b1b;
}

.blood-type-badge.b-positive {
    background: #eff6ff;
    border-color: #bfdbfe;
    color: #1e40af;
}

.blood-type-badge.b-negative {
    background: #dbeafe;
    border-color: #93c5fd;
    color: #1e3a8a;
}

.blood-type-badge.ab-positive {
    background: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.blood-type-badge.ab-negative {
    background: #ecfdf5;
    border-color: #86efac;
    color: #14532d;
}

.blood-type-badge.o-positive {
    background: #fffbeb;
    border-color: #fed7aa;
    color: #92400e;
}

.blood-type-badge.o-negative {
    background: #fefce8;
    border-color: #fde68a;
    color: #a16207;
}

/* Enhanced pagination */
.pagination-button {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(229, 231, 235, 0.4);
    border-radius: 6px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.pagination-button:hover {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.08), rgba(14, 165, 233, 0.03));
    border-color: rgba(14, 165, 233, 0.3);
    transform: translateY(-1px);
}

/* Enhanced Professional Add Patient Button */
.add-patient-button {
    background: linear-gradient(135deg, #1e40af, #1d4ed8);
    border: 1px solid #1e3a8a;
    border-radius: 6px;
    color: white;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.add-patient-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.add-patient-button:hover {
    background: linear-gradient(135deg, #1d4ed8, #2563eb);
    border-color: #1e40af;
    transform: translateY(-1px);
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-patient-button:hover::before {
    left: 100%;
}

.add-patient-button:active {
    transform: translateY(0);
    box-shadow:
        0 1px 2px rgba(0, 0, 0, 0.1),
        0 1px 1px rgba(0, 0, 0, 0.06);
}

/* QR Scanner Modal */
.qr-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.qr-modal.active {
    opacity: 1;
    visibility: visible;
}

.qr-modal-content {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.qr-modal.active .qr-modal-content {
    transform: scale(1);
}

#qr-video {
    width: 100%;
    border-radius: 12px;
    background: #000;
}

/* Government-Level Responsive Design */
@media (max-width: 768px) {
    .patients-table-card {
        border-radius: 4px;
        margin: 0 0.5rem;
    }

    .patients-table th,
    .patients-table td {
        padding: 0.75rem 0.875rem;
        font-size: 0.8rem;
    }

    .patient-avatar {
        width: 32px;
        height: 32px;
    }

    .action-button {
        width: 28px;
        height: 28px;
        font-size: 0.7rem;
    }

    .government-filter-button {
        min-width: 100px;
        font-size: 0.75rem;
    }

    .government-search-input {
        font-size: 0.8rem;
    }
}

@media (max-width: 640px) {
    .flex.items-center.justify-between {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .flex.items-center.space-x-3 {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .government-filter-button {
        min-width: 80px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
