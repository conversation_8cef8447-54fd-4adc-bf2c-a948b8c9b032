{% extends 'base.html' %}

{% block title %}Healthcare Dashboard - ZimHealth-ID{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}">
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Sleek Professional Dashboard Header -->
    <div class="dashboard-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="header-content">
                <!-- Left Section: Clean Professional Title -->
                <div class="header-left">
                    <h1 class="header-title">Healthcare Dashboard</h1>
                    <p class="header-subtitle">Patient Care Management</p>
                </div>

                <!-- Center Section: Quick Search -->
                <div class="header-center flex-1 max-w-md mx-8">
                    <div class="relative">
                        <input type="text"
                               class="dashboard-search w-full px-4 py-2 pl-10 pr-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500 transition-colors placeholder-gray-400"
                               placeholder="Quick patient search...">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <!-- Right Section: User Information -->
                <div class="header-right">
                    <div class="user-info">
                        <p class="user-name">{{ user.first_name|default:user.username|title }}</p>
                        <p class="user-timestamp">{{ user.last_login|date:"M d, Y H:i" }}</p>
                    </div>
                    <div class="user-avatar">
                        <i class="fas fa-user-md"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Dashboard Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <!-- Command Center Header -->
        <div class="bg-gradient-to-r from-medical-600 to-health-600 rounded-xl shadow-lg p-6 mb-8 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-2xl font-bold mb-2">ZimHealth-ID Command Center</h2>
                    <p class="text-medical-100">Real-time healthcare management dashboard</p>
                </div>
                <div class="text-right">
                    <div class="text-3xl font-bold" id="live-clock">{{ current_time|time:"H:i:s" }}</div>
                    <div class="text-medical-100">{{ today|date:"l, F d, Y" }}</div>
                </div>
            </div>

            <!-- System Status Indicators -->
            <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="bg-white bg-opacity-20 rounded-lg p-3 text-center">
                    <div class="w-3 h-3 bg-green-400 rounded-full mx-auto mb-2 pulse"></div>
                    <div class="text-xs font-medium">Database</div>
                    <div class="text-xs opacity-75">{{ system_health.database_status|title }}</div>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-3 text-center">
                    <div class="w-3 h-3 bg-green-400 rounded-full mx-auto mb-2 pulse"></div>
                    <div class="text-xs font-medium">Server</div>
                    <div class="text-xs opacity-75">{{ system_health.server_load|title }}</div>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-3 text-center">
                    <div class="w-3 h-3 bg-yellow-400 rounded-full mx-auto mb-2 pulse"></div>
                    <div class="text-xs font-medium">Response</div>
                    <div class="text-xs opacity-75">{{ system_health.response_time }}</div>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-3 text-center">
                    <div class="w-3 h-3 bg-green-400 rounded-full mx-auto mb-2 pulse"></div>
                    <div class="text-xs font-medium">Uptime</div>
                    <div class="text-xs opacity-75">{{ system_health.uptime }}</div>
                </div>
            </div>
        </div>

        <!-- Critical Alerts Banner -->
        {% if overdue_appointments or expiring_prescriptions or patients_no_recent_visit %}
        <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-8 rounded-r-lg">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-red-400 text-xl animate-pulse"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Critical Alerts Require Attention</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc list-inside space-y-1">
                            {% if overdue_appointments %}
                            <li>{{ overdue_appointments }} overdue appointment{{ overdue_appointments|pluralize }}</li>
                            {% endif %}
                            {% if expiring_prescriptions %}
                            <li>{{ expiring_prescriptions }} prescription{{ expiring_prescriptions|pluralize }} expiring within 7 days</li>
                            {% endif %}
                            {% if patients_no_recent_visit %}
                            <li>{{ patients_no_recent_visit }} patient{{ patients_no_recent_visit|pluralize }} with no recent visits (>6 months)</li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
                <div class="ml-auto">
                    <button onclick="showAlertDetails()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm font-medium">
                        View Details
                    </button>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Enhanced Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Patients -->
            <a href="{% url 'api:patients' %}" class="stat-card patients p-6 block hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon medical">
                            <i class="fas fa-users text-medical-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-semibold text-gray-500 uppercase tracking-wide">Total Patients</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_patients|default:"1,247" }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-sm text-health-600 font-medium">
                        <i class="fas fa-arrow-up mr-1"></i>
                        <span>+12% from last month</span>
                    </div>
                </div>
            </a>

            <!-- Today's Appointments -->
            <a href="{% url 'api:appointments' %}" class="stat-card appointments p-6 block hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon health">
                            <i class="fas fa-calendar-check text-health-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-semibold text-gray-500 uppercase tracking-wide">Today's Appointments</p>
                        <p class="text-2xl font-bold text-gray-900">{{ todays_appointments|default:"24" }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-sm text-medical-600 font-medium">
                        <i class="fas fa-clock mr-1"></i>
                        <span>{{ pending_appointments|default:"8" }} pending</span>
                    </div>
                </div>
            </a>

            <!-- Medical Records -->
            <a href="{% url 'api:medical_records' %}" class="stat-card records p-6 block hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon blue">
                            <i class="fas fa-file-medical text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-semibold text-gray-500 uppercase tracking-wide">Medical Records</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_records|default:"8,432" }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-sm text-blue-600 font-medium">
                        <i class="fas fa-plus mr-1"></i>
                        <span>{{ new_records|default:"156" }} this week</span>
                    </div>
                </div>
            </a>

            <!-- Active Prescriptions -->
            <a href="{% url 'api:prescriptions' %}" class="stat-card prescriptions p-6 block hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-icon purple">
                            <i class="fas fa-pills text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-semibold text-gray-500 uppercase tracking-wide">Active Prescriptions</p>
                        <p class="text-2xl font-bold text-gray-900">{{ active_prescriptions|default:"342" }}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-sm text-purple-600 font-medium">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        <span>{{ expiring_prescriptions|default:"12" }} expiring soon</span>
                    </div>
                </div>
            </a>
        </div>

        <!-- Unified Quick Actions Ribbon -->
        <div class="mb-8">
            <div class="quick-actions-ribbon">
                <div class="px-8 py-6">
                    <div class="text-center mb-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Healthcare Command Center</h3>
                        <p class="text-sm text-gray-600">Essential functions for comprehensive patient care management</p>
                    </div>
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                        <!-- Primary Actions -->
                        <a href="{% url 'api:patient_create' %}" class="quick-action medical flex flex-col items-center p-4">
                            <div class="stat-icon medical mb-3">
                                <i class="fas fa-user-plus text-medical-600 text-xl"></i>
                            </div>
                            <span class="text-sm font-semibold text-gray-900">Register Patient</span>
                            <span class="text-xs text-gray-500 mt-1">New enrollment</span>
                        </a>
                        <a href="{% url 'api:appointment_create' %}" class="quick-action health flex flex-col items-center p-4">
                            <div class="stat-icon health mb-3">
                                <i class="fas fa-calendar-plus text-health-600 text-xl"></i>
                            </div>
                            <span class="text-sm font-semibold text-gray-900">Schedule Visit</span>
                            <span class="text-xs text-gray-500 mt-1">Book appointment</span>
                        </a>
                        <a href="{% url 'api:medical_record_create' %}" class="quick-action blue flex flex-col items-center p-4">
                            <div class="stat-icon blue mb-3">
                                <i class="fas fa-file-medical-alt text-blue-600 text-xl"></i>
                            </div>
                            <span class="text-sm font-semibold text-gray-900">Medical Record</span>
                            <span class="text-xs text-gray-500 mt-1">Create new</span>
                        </a>
                        <a href="{% url 'api:prescription_create' %}" class="quick-action purple flex flex-col items-center p-4">
                            <div class="stat-icon purple mb-3">
                                <i class="fas fa-prescription-bottle text-purple-600 text-xl"></i>
                            </div>
                            <span class="text-sm font-semibold text-gray-900">Prescribe</span>
                            <span class="text-xs text-gray-500 mt-1">Issue medication</span>
                        </a>
                        <!-- Additional Unique Actions -->
                        <a href="{% url 'api:patients' %}" class="quick-action orange flex flex-col items-center p-4">
                            <div class="stat-icon orange mb-3">
                                <i class="fas fa-search text-orange-600 text-xl"></i>
                            </div>
                            <span class="text-sm font-semibold text-gray-900">Patient Search</span>
                            <span class="text-xs text-gray-500 mt-1">Find records</span>
                        </a>
                        <a href="{% url 'api:appointment_create' %}" class="quick-action red flex flex-col items-center p-4" onclick="showEmergencyModal(); return false;">
                            <div class="stat-icon red mb-3">
                                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                            </div>
                            <span class="text-sm font-semibold text-gray-900">Emergency</span>
                            <span class="text-xs text-gray-500 mt-1">Urgent care</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Dashboard Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Enhanced Recent Activity Card -->
            <div class="lg:col-span-2">
                <div class="activity-card">
                    <div class="activity-header">
                        <h3 class="activity-title">
                            <div class="activity-title-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            Recent Healthcare Activity
                        </h3>
                        <p class="activity-subtitle">Latest patient interactions and system updates</p>
                    </div>
                    <div class="activity-content">
                        {% if recent_activities %}
                            <div class="space-y-0">
                                {% for activity in recent_activities %}
                                    <div class="activity-item flex items-start space-x-4">
                                        <div class="flex-shrink-0">
                                            <div class="activity-icon">
                                                <i class="fas fa-{{ activity.icon }} text-medical-600 text-sm"></i>
                                            </div>
                                        </div>
                                        <div class="activity-details">
                                            <p class="activity-title-text">{{ activity.title }}</p>
                                            <p class="activity-description">{{ activity.description }}</p>
                                            <p class="activity-timestamp">{{ activity.timestamp|timesince }} ago</p>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="activity-empty">
                                <div class="activity-empty-icon">
                                    <i class="fas fa-history text-gray-400 text-xl"></i>
                                </div>
                                <p class="activity-empty-title">No recent activity</p>
                                <p class="activity-empty-subtitle">Patient interactions will appear here</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Enhanced Sidebar -->
            <div class="space-y-6">
                <!-- System Status & Alerts -->
                <div class="content-card">
                    <div class="card-header px-6 py-4">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-heartbeat text-health-500 mr-2"></i>System Status
                        </h3>
                        <p class="text-sm text-gray-600 mt-1">Real-time healthcare system monitoring</p>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-health-500 rounded-full animate-pulse"></div>
                                <span class="text-sm font-medium text-gray-900">Database Connection</span>
                            </div>
                            <span class="status-badge">Online</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-health-500 rounded-full animate-pulse"></div>
                                <span class="text-sm font-medium text-gray-900">Patient Records</span>
                            </div>
                            <span class="status-badge">Synced</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                                <span class="text-sm font-medium text-gray-900">Backup Status</span>
                            </div>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                Pending
                            </span>
                        </div>
                        <div class="pt-3 border-t border-gray-100">
                            <div class="text-center">
                                <p class="text-xs text-gray-500">Last system check</p>
                                <p class="text-sm font-semibold text-gray-900">2 minutes ago</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Upcoming Appointments -->
                <div class="content-card">
                    <div class="card-header px-6 py-4">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-calendar-alt text-health-500 mr-2"></i>Upcoming Appointments
                        </h3>
                        <p class="text-sm text-gray-600 mt-1">Next scheduled patient visits</p>
                    </div>
                    <div class="p-6">
                        {% if upcoming_appointments %}
                            <div class="space-y-3">
                                {% for appointment in upcoming_appointments %}
                                    <div class="appointment-card p-3">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <p class="font-semibold text-gray-900">{{ appointment.patient.full_name }}</p>
                                                <p class="text-sm text-gray-600">{{ appointment.date|date:"M d" }} at {{ appointment.time|time:"H:i" }}</p>
                                            </div>
                                            <div class="text-right">
                                                <span class="status-badge">
                                                    {{ appointment.appointment_type|title }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-8">
                                <div class="activity-icon mx-auto mb-4">
                                    <i class="fas fa-calendar-times text-gray-400 text-xl"></i>
                                </div>
                                <p class="text-gray-500 font-medium">No upcoming appointments</p>
                                <p class="text-gray-400 text-sm mt-1">Schedule is clear</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script src="{% static 'assets/js/dashboard.js' %}"></script>
{% endblock %}
{% endblock %}
