{% extends 'base.html' %}
{% load static %}

{% block title %}Medical Records Management - ZimHealth-ID{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/medical_records.css' %}">
{% endblock %}

{% block content %}
<div class="medical-records-container">
    <!-- Professional Medical Records Header -->
    <div class="medical-records-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Medical Records Management</h1>
                        <p class="text-gray-600 mt-1 text-sm">Professional healthcare documentation and medical history management</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="new-medical-record-button flex items-center space-x-2">
                            <i class="fas fa-plus"></i>
                            <span>New Medical Record</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Content with Professional Stats -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <!-- Professional Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon total">
                        <i class="fas fa-file-medical text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Records</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_records|default:156 }}</p>
                    </div>
                </div>
            </div>

            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon recent">
                        <i class="fas fa-clock text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Recent (7 days)</p>
                        <p class="text-2xl font-bold text-gray-900">{{ recent_records|default:18 }}</p>
                    </div>
                </div>
            </div>

            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon critical">
                        <i class="fas fa-exclamation-triangle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Critical Cases</p>
                        <p class="text-2xl font-bold text-gray-900">{{ critical_records|default:4 }}</p>
                    </div>
                </div>
            </div>

            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon pending">
                        <i class="fas fa-hourglass-half text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Pending Review</p>
                        <p class="text-2xl font-bold text-gray-900">{{ pending_records|default:7 }}</p>
                    </div>
                </div>
            </div>
        </div>
        <!-- Government-Level Professional Search Controls -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <!-- Left: Enhanced Search Bar with QR Scanner -->
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <input type="text" id="medical-record-search" name="search"
                               class="medical-records-search-input w-full pl-10 pr-12 py-2.5"
                               placeholder="Search records by patient, diagnosis, or use QR scanner...">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-500"></i>
                        </div>
                        <!-- QR Code Scanner inside search bar -->
                        <button type="button" id="medical-record-qr-scanner-btn" class="medical-records-search-qr-button absolute inset-y-0 right-0 pr-3 flex items-center" title="Scan QR Code">
                            <i class="fas fa-qrcode"></i>
                        </button>
                    </div>
                </div>

                <!-- Right: Filter Controls -->
                <div class="flex items-center space-x-3">
                    <!-- Record Type Filter -->
                    <select id="type-filter" name="type" class="medical-records-filter-button">
                        <option value="">All Types</option>
                        <option value="consultation">Consultation</option>
                        <option value="emergency">Emergency</option>
                        <option value="follow_up">Follow-up</option>
                        <option value="surgery">Surgery</option>
                        <option value="screening">Screening</option>
                    </select>

                    <!-- Status Filter -->
                    <select id="status-filter" name="status" class="medical-records-filter-button">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="pending">Pending</option>
                        <option value="archived">Archived</option>
                        <option value="critical">Critical</option>
                    </select>

                    <!-- Facility Filter -->
                    <select id="facility-filter" name="facility" class="medical-records-filter-button">
                        <option value="">All Facilities</option>
                        <option value="central">Central Hospital</option>
                        <option value="medical">Medical Center</option>
                        <option value="clinic">Health Clinic</option>
                    </select>

                    <!-- Date Range Filters -->
                    <input type="date" id="date-from-filter" class="medical-records-filter-button" title="From Date">
                    <span class="text-gray-400">to</span>
                    <input type="date" id="date-to-filter" class="medical-records-filter-button" title="To Date">
                </div>
            </div>

            <!-- Results Count -->
            <div class="mt-3 flex items-center justify-between">
                <div class="results-count text-sm text-gray-600 font-medium">
                    Loading medical records...
                </div>
                <div class="text-xs text-gray-500">
                    Professional Medical Documentation System
                </div>
            </div>
        </div>

        <!-- Professional Medical Records Management -->
        <div class="medical-records-table-card">
            <div class="medical-records-table-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Healthcare Medical Records Registry</h3>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <span>{{ medical_records.count|default:"3" }} records</span>
                    </div>
                </div>
            </div>

            <!-- Professional Medical Records Table -->
            <div class="medical-records-list">
                <div class="overflow-x-auto">
                    <table class="medical-records-table">
                        <thead>
                            <tr>
                                <th class="patient-column">Patient</th>
                                <th class="date-column">Date & Time</th>
                                <th class="provider-column">Healthcare Provider</th>
                                <th class="type-column">Record Type</th>
                                <th class="diagnosis-column">Diagnosis</th>
                                <th class="status-column">Status</th>
                                <th class="actions-column">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in medical_records %}
                                <tr>
                                    <!-- Patient Column -->
                                    <td class="patient-column">
                                        <div class="flex items-center space-x-3">
                                            <div class="record-status-indicator {{ record.status|default:'active' }}"></div>
                                            <div class="medical-record-avatar">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-semibold text-gray-900">{{ record.patient.full_name|default:"Sarah Johnson" }}</div>
                                                <div class="text-xs text-gray-500">{{ record.patient.zimhealth_id|default:"ZH-2024-001" }}</div>
                                            </div>
                                        </div>
                                    </td>

                                    <!-- Date & Time Column -->
                                    <td class="date-column">
                                        <div class="text-sm font-medium text-gray-900">{{ record.date|date:"M d, Y"|default:"Jul 28, 2025" }}</div>
                                        <div class="text-xs text-gray-500">{{ record.date|time:"H:i"|default:"14:30" }}</div>
                                    </td>

                                    <!-- Healthcare Provider Column -->
                                    <td class="provider-column">
                                        <div class="text-sm font-medium text-gray-900">{{ record.doctor_name|default:"Dr. Smith" }}</div>
                                        <div class="text-xs text-gray-500 facility-text">{{ record.facility_name|default:"Central Hospital" }}</div>
                                    </td>

                                    <!-- Record Type Column -->
                                    <td class="type-column">
                                        <span class="record-type-badge">{{ record.get_record_type_display|default:"Consultation" }}</span>
                                        {% if record.priority != 'normal' %}
                                            <div class="priority-indicator {{ record.priority|default:'normal' }} mt-1 text-xs">{{ record.get_priority_display|default:"NORMAL" }}</div>
                                        {% endif %}
                                    </td>

                                    <!-- Diagnosis Column -->
                                    <td class="diagnosis-column">
                                        <div class="text-sm text-gray-900" title="{{ record.diagnosis|default:'Hypertension, Type 2 Diabetes' }}">
                                            {{ record.diagnosis|default:"Hypertension, Type 2 Diabetes"|truncatechars:40 }}
                                        </div>
                                    </td>

                                    <!-- Status Column -->
                                    <td class="status-column">
                                        <span class="record-status-badge {{ record.status|default:'active' }}">{{ record.get_status_display|default:"Active" }}</span>
                                    </td>

                                    <!-- Actions Column -->
                                    <td class="actions-column">
                                        <div class="flex items-center justify-center space-x-1">
                                            <button class="medical-record-action-button view" data-action="view" data-record-id="{{ record.id|default:'MR-001' }}" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="medical-record-action-button edit" data-action="edit" data-record-id="{{ record.id|default:'MR-001' }}" title="Edit Record">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="medical-record-action-button download" data-action="download" data-record-id="{{ record.id|default:'MR-001' }}" title="Download PDF">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="medical-record-action-button prescription" data-action="prescription" data-record-id="{{ record.id|default:'MR-001' }}" title="Add Prescription">
                                                <i class="fas fa-pills"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Medical Record Details Row (if expanded) -->
                                {% if record.notes or record.treatment %}
                                    <tr class="details-row">
                                        <td colspan="7" class="px-6 py-3 bg-gray-50 border-b border-gray-200">
                                            {% if record.diagnosis %}
                                                <p class="text-xs text-gray-600 mb-1"><strong>Full Diagnosis:</strong> {{ record.diagnosis }}</p>
                                            {% endif %}
                                            {% if record.treatment %}
                                                <p class="text-xs text-gray-600 mb-1"><strong>Treatment:</strong> {{ record.treatment }}</p>
                                            {% endif %}
                                            {% if record.notes %}
                                                <p class="text-xs text-gray-600"><strong>Notes:</strong> {{ record.notes }}</p>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endif %}
                            {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center py-12">
                                        <div class="flex flex-col items-center">
                                            <div class="medical-record-avatar mx-auto mb-4">
                                                <i class="fas fa-file-medical text-gray-400 text-2xl"></i>
                                            </div>
                                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No medical records found</h3>
                                            <p class="text-gray-500 mb-4">Get started by creating your first medical record or use the search filters.</p>
                                            <button class="new-medical-record-button">
                                                <i class="fas fa-plus mr-2"></i>New Medical Record
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Enhanced Pagination -->
            {% if medical_records.has_other_pages %}
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-500">
                            Showing {{ medical_records.start_index }} to {{ medical_records.end_index }} of {{ medical_records.paginator.count }} results
                        </div>
                        <div class="flex items-center space-x-2">
                            {% if medical_records.has_previous %}
                                <a href="?page={{ medical_records.previous_page_number }}"
                                   class="pagination-button">
                                    Previous
                                </a>
                            {% endif %}

                            <span class="px-3 py-2 text-sm font-medium text-gray-700">
                                Page {{ medical_records.number }} of {{ medical_records.paginator.num_pages }}
                            </span>

                            {% if medical_records.has_next %}
                                <a href="?page={{ medical_records.next_page_number }}"
                                   class="pagination-button">
                                    Next
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'assets/js/medical_records.js' %}"></script>
{% endblock %}
