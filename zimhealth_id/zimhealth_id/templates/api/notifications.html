{% extends 'base.html' %}

{% block title %}Notifications - ZimHealth-ID{% endblock %}

{% block extra_css %}
{% load static %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}">
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:dashboard' %}" class="text-medical-600 hover:text-medical-700 transition-colors">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Notifications</h1>
                            <p class="text-gray-600 mt-1">System alerts and updates</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="bg-medical-100 text-medical-800 px-3 py-1 rounded-full text-sm font-medium">
                            {{ unread_count }} unread
                        </span>
                        <button onclick="markAllAsRead()" class="bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700 transition-colors font-medium">
                            <i class="fas fa-check-double mr-2"></i>Mark All Read
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-xl shadow-lg border border-gray-100">
            {% if notifications %}
                <div class="divide-y divide-gray-100">
                    {% for notification in notifications %}
                        <div class="notification-item p-6 {% if not notification.read %}bg-blue-50{% endif %} hover:bg-gray-50 transition-colors">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full flex items-center justify-center
                                        {% if notification.type == 'success' %}bg-green-100{% elif notification.type == 'warning' %}bg-yellow-100{% elif notification.type == 'error' %}bg-red-100{% else %}bg-blue-100{% endif %}">
                                        {% if notification.type == 'success' %}
                                            <i class="fas fa-check-circle text-green-600"></i>
                                        {% elif notification.type == 'warning' %}
                                            <i class="fas fa-exclamation-triangle text-yellow-600"></i>
                                        {% elif notification.type == 'error' %}
                                            <i class="fas fa-exclamation-circle text-red-600"></i>
                                        {% else %}
                                            <i class="fas fa-info-circle text-blue-600"></i>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-lg font-semibold text-gray-900">{{ notification.title }}</h3>
                                        <div class="flex items-center space-x-2">
                                            {% if not notification.read %}
                                                <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                            {% endif %}
                                            <span class="text-sm text-gray-500">{{ notification.created_at|timesince }} ago</span>
                                        </div>
                                    </div>
                                    <p class="text-gray-600 mt-1">{{ notification.message }}</p>
                                    {% if not notification.read %}
                                        <div class="mt-3">
                                            <button onclick="markAsRead({{ notification.id }})" class="text-medical-600 hover:text-medical-700 text-sm font-medium">
                                                Mark as read
                                            </button>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-bell text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">No notifications</h3>
                    <p class="text-gray-600">You're all caught up! Check back later for updates.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function markAsRead(notificationId) {
    fetch(`/api/ajax/notifications/mark-read/${notificationId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function markAllAsRead() {
    // Implementation for marking all notifications as read
    const unreadNotifications = document.querySelectorAll('.notification-item.bg-blue-50');
    unreadNotifications.forEach(notification => {
        // Mark each as read
        notification.classList.remove('bg-blue-50');
    });
    
    // Update unread count
    const unreadCount = document.querySelector('.bg-medical-100');
    if (unreadCount) {
        unreadCount.textContent = '0 unread';
    }
}
</script>
{% endblock %}
