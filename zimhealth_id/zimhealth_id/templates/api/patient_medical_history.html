{% extends 'base.html' %}

{% block title %}Medical History - {{ patient.full_name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:patient_detail' patient.zimhealth_id %}" class="text-medical-600 hover:text-medical-700 transition-colors">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Complete Medical History</h1>
                            <p class="text-gray-600 mt-1">{{ patient.full_name }} ({{ patient.zimhealth_id }})</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button onclick="window.print()" class="bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700 transition-colors font-medium">
                            <i class="fas fa-print mr-2"></i>Print History
                        </button>
                        <a href="{% url 'api:medical_record_create_for_patient' patient.zimhealth_id %}" class="bg-health-600 text-white px-4 py-2 rounded-lg hover:bg-health-700 transition-colors font-medium">
                            <i class="fas fa-plus mr-2"></i>Add Record
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-file-medical text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Records</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_records }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-pills text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Prescriptions</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_prescriptions }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-calendar text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Appointments</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_appointments }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Timeline -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-bold text-gray-900">Medical Records Timeline</h3>
                        <p class="text-gray-600 text-sm mt-1">Chronological history of medical visits</p>
                    </div>
                    
                    {% if medical_records %}
                        <div class="divide-y divide-gray-100">
                            {% for record in medical_records %}
                            <div class="p-6 hover:bg-gray-50 transition-colors">
                                <div class="flex items-start space-x-4">
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 bg-medical-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-file-medical text-medical-600"></i>
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="text-lg font-semibold text-gray-900">{{ record.date|date:"M d, Y" }}</h4>
                                            <a href="{% url 'api:medical_record_detail' record.id %}" class="text-medical-600 hover:text-medical-700 text-sm font-medium">
                                                View Details <i class="fas fa-external-link-alt ml-1"></i>
                                            </a>
                                        </div>
                                        <div class="grid grid-cols-2 gap-4 mb-3">
                                            <div>
                                                <p class="text-sm text-gray-500">Doctor</p>
                                                <p class="font-medium">{{ record.doctor_name }}</p>
                                            </div>
                                            <div>
                                                <p class="text-sm text-gray-500">Facility</p>
                                                <p class="font-medium">{{ record.facility_name }}</p>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <p class="text-sm text-gray-500 mb-1">Diagnosis</p>
                                            <p class="text-gray-800">{{ record.diagnosis|truncatewords:20 }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500 mb-1">Treatment</p>
                                            <p class="text-gray-800">{{ record.treatment|truncatewords:15 }}</p>
                                        </div>
                                        
                                        <!-- Vital Signs -->
                                        {% if record.temperature or record.blood_pressure or record.heart_rate %}
                                        <div class="mt-3 pt-3 border-t border-gray-100">
                                            <p class="text-sm text-gray-500 mb-2">Vital Signs</p>
                                            <div class="flex flex-wrap gap-4 text-sm">
                                                {% if record.temperature %}
                                                <span class="bg-red-50 text-red-700 px-2 py-1 rounded">Temp: {{ record.temperature }}°C</span>
                                                {% endif %}
                                                {% if record.blood_pressure %}
                                                <span class="bg-blue-50 text-blue-700 px-2 py-1 rounded">BP: {{ record.blood_pressure }}</span>
                                                {% endif %}
                                                {% if record.heart_rate %}
                                                <span class="bg-green-50 text-green-700 px-2 py-1 rounded">HR: {{ record.heart_rate }} bpm</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <!-- Pagination -->
                        {% if medical_records.has_other_pages %}
                        <div class="px-6 py-4 border-t border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-700">
                                    Showing {{ medical_records.start_index }} to {{ medical_records.end_index }} of {{ medical_records.paginator.count }} records
                                </div>
                                <div class="flex space-x-2">
                                    {% if medical_records.has_previous %}
                                        <a href="?page={{ medical_records.previous_page_number }}" class="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                                            Previous
                                        </a>
                                    {% endif %}
                                    {% if medical_records.has_next %}
                                        <a href="?page={{ medical_records.next_page_number }}" class="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                                            Next
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="p-12 text-center">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-file-medical text-gray-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Medical Records</h3>
                            <p class="text-gray-600 mb-4">This patient doesn't have any medical records yet.</p>
                            <a href="{% url 'api:medical_record_create_for_patient' patient.zimhealth_id %}" class="bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700 transition-colors font-medium">
                                <i class="fas fa-plus mr-2"></i>Add First Record
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Patient Summary -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Patient Summary</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Age</span>
                            <span class="font-semibold">{{ patient.age }} years</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Gender</span>
                            <span class="font-semibold">{{ patient.get_gender_display }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Blood Type</span>
                            <span class="font-semibold">{{ patient.blood_type|default:"Unknown" }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Last Visit</span>
                            <span class="font-semibold">
                                {% if patient.last_visit %}
                                    {{ patient.last_visit|date:"M d, Y" }}
                                {% else %}
                                    Never
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    
                    {% if patient.allergies %}
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <p class="text-sm font-medium text-gray-500 mb-2">Allergies</p>
                        <div class="flex flex-wrap gap-2">
                            {% for allergy in patient.allergies %}
                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">{{ allergy }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Recent Prescriptions -->
                {% if prescriptions %}
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Recent Prescriptions</h3>
                    <div class="space-y-3">
                        {% for prescription in prescriptions %}
                        <div class="border border-gray-200 rounded-lg p-3">
                            <div class="flex items-center justify-between mb-1">
                                <h4 class="font-semibold text-sm">{{ prescription.medication }}</h4>
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {% if prescription.status == 'active' %}bg-green-100 text-green-800
                                    {% elif prescription.status == 'completed' %}bg-blue-100 text-blue-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ prescription.get_status_display }}
                                </span>
                            </div>
                            <p class="text-xs text-gray-600">{{ prescription.dosage }} - {{ prescription.get_frequency_display }}</p>
                            <p class="text-xs text-gray-500 mt-1">{{ prescription.created_at|date:"M d, Y" }}</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Recent Appointments -->
                {% if appointments %}
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Recent Appointments</h3>
                    <div class="space-y-3">
                        {% for appointment in appointments %}
                        <div class="border border-gray-200 rounded-lg p-3">
                            <div class="flex items-center justify-between mb-1">
                                <h4 class="font-semibold text-sm">{{ appointment.get_appointment_type_display }}</h4>
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {% if appointment.status == 'scheduled' %}bg-blue-100 text-blue-800
                                    {% elif appointment.status == 'completed' %}bg-green-100 text-green-800
                                    {% elif appointment.status == 'cancelled' %}bg-red-100 text-red-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ appointment.get_status_display }}
                                </span>
                            </div>
                            <p class="text-xs text-gray-600">{{ appointment.doctor_name }}</p>
                            <p class="text-xs text-gray-500 mt-1">{{ appointment.date|date:"M d, Y" }} at {{ appointment.time|time:"H:i" }}</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Quick Actions -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="{% url 'api:medical_record_create_for_patient' patient.zimhealth_id %}" class="block w-full bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700 transition-colors text-center font-medium">
                            <i class="fas fa-plus mr-2"></i>Add Medical Record
                        </a>
                        <a href="{% url 'api:appointment_create_for_patient' patient.zimhealth_id %}" class="block w-full bg-health-600 text-white px-4 py-2 rounded-lg hover:bg-health-700 transition-colors text-center font-medium">
                            <i class="fas fa-calendar-plus mr-2"></i>Schedule Appointment
                        </a>
                        <button onclick="window.print()" class="block w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors font-medium">
                            <i class="fas fa-print mr-2"></i>Print History
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
