{% extends 'base.html' %}

{% block title %}Medical Record - {{ medical_record.patient.full_name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:medical_records' %}" class="text-medical-600 hover:text-medical-700 transition-colors">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Medical Record</h1>
                            <p class="text-gray-600 mt-1">{{ medical_record.patient.full_name }} - {{ medical_record.date|date:"M d, Y" }}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:medical_record_edit' medical_record.id %}" class="bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700 transition-colors font-medium">
                            <i class="fas fa-edit mr-2"></i>Edit Record
                        </a>
                        <a href="{% url 'api:prescription_create_for_record' medical_record.id %}" class="bg-health-600 text-white px-4 py-2 rounded-lg hover:bg-health-700 transition-colors font-medium">
                            <i class="fas fa-pills mr-2"></i>Add Prescription
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Patient Information -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Patient Information</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Patient Name</p>
                            <p class="text-lg font-semibold text-gray-900">{{ medical_record.patient.full_name }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">ZimHealth ID</p>
                            <p class="text-lg font-semibold text-gray-900">{{ medical_record.patient.zimhealth_id }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Age</p>
                            <p class="text-lg font-semibold text-gray-900">{{ medical_record.patient.age }} years</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Blood Type</p>
                            <p class="text-lg font-semibold text-gray-900">{{ medical_record.patient.blood_type|default:"Unknown" }}</p>
                        </div>
                    </div>
                </div>

                <!-- Visit Details -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Visit Details</h3>
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Date & Time</p>
                            <p class="text-lg font-semibold text-gray-900">{{ medical_record.date|date:"M d, Y H:i" }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Facility</p>
                            <p class="text-lg font-semibold text-gray-900">{{ medical_record.facility_name }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Doctor</p>
                            <p class="text-lg font-semibold text-gray-900">{{ medical_record.doctor_name }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Created By</p>
                            <p class="text-lg font-semibold text-gray-900">{{ medical_record.created_by.get_full_name|default:medical_record.created_by.username }}</p>
                        </div>
                    </div>

                    <!-- Diagnosis -->
                    <div class="mb-6">
                        <h4 class="text-md font-bold text-gray-900 mb-2">Diagnosis</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-800">{{ medical_record.diagnosis }}</p>
                        </div>
                    </div>

                    <!-- Treatment -->
                    <div class="mb-6">
                        <h4 class="text-md font-bold text-gray-900 mb-2">Treatment</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-800">{{ medical_record.treatment }}</p>
                        </div>
                    </div>

                    {% if medical_record.notes %}
                    <!-- Notes -->
                    <div>
                        <h4 class="text-md font-bold text-gray-900 mb-2">Additional Notes</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-800">{{ medical_record.notes }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Prescriptions -->
                {% if prescriptions %}
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Prescriptions</h3>
                    <div class="space-y-4">
                        {% for prescription in prescriptions %}
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-semibold text-gray-900">{{ prescription.medication }}</h4>
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {% if prescription.status == 'active' %}bg-green-100 text-green-800
                                    {% elif prescription.status == 'completed' %}bg-blue-100 text-blue-800
                                    {% elif prescription.status == 'discontinued' %}bg-red-100 text-red-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ prescription.get_status_display }}
                                </span>
                            </div>
                            <div class="grid grid-cols-3 gap-4 text-sm">
                                <div>
                                    <p class="text-gray-500">Dosage</p>
                                    <p class="font-medium">{{ prescription.dosage }}</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">Frequency</p>
                                    <p class="font-medium">{{ prescription.get_frequency_display }}</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">Duration</p>
                                    <p class="font-medium">{{ prescription.duration }}</p>
                                </div>
                            </div>
                            {% if prescription.instructions %}
                            <div class="mt-2">
                                <p class="text-gray-500 text-sm">Instructions</p>
                                <p class="text-sm">{{ prescription.instructions }}</p>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Vital Signs -->
                {% if medical_record.temperature or medical_record.blood_pressure_systolic or medical_record.heart_rate or medical_record.weight or medical_record.height %}
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Vital Signs</h3>
                    <div class="space-y-3">
                        {% if medical_record.temperature %}
                        <div class="flex justify-between">
                            <span class="text-gray-600">Temperature</span>
                            <span class="font-semibold">{{ medical_record.temperature }}°C</span>
                        </div>
                        {% endif %}
                        {% if medical_record.blood_pressure %}
                        <div class="flex justify-between">
                            <span class="text-gray-600">Blood Pressure</span>
                            <span class="font-semibold">{{ medical_record.blood_pressure }} mmHg</span>
                        </div>
                        {% endif %}
                        {% if medical_record.heart_rate %}
                        <div class="flex justify-between">
                            <span class="text-gray-600">Heart Rate</span>
                            <span class="font-semibold">{{ medical_record.heart_rate }} bpm</span>
                        </div>
                        {% endif %}
                        {% if medical_record.weight %}
                        <div class="flex justify-between">
                            <span class="text-gray-600">Weight</span>
                            <span class="font-semibold">{{ medical_record.weight }} kg</span>
                        </div>
                        {% endif %}
                        {% if medical_record.height %}
                        <div class="flex justify-between">
                            <span class="text-gray-600">Height</span>
                            <span class="font-semibold">{{ medical_record.height }} cm</span>
                        </div>
                        {% endif %}
                        {% if medical_record.bmi %}
                        <div class="flex justify-between border-t pt-2">
                            <span class="text-gray-600">BMI</span>
                            <span class="font-semibold">{{ medical_record.bmi }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Quick Actions -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="{% url 'api:patient_detail' medical_record.patient.zimhealth_id %}" class="block w-full bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700 transition-colors text-center font-medium">
                            <i class="fas fa-user mr-2"></i>View Patient
                        </a>
                        <a href="{% url 'api:appointment_create_for_patient' medical_record.patient.zimhealth_id %}" class="block w-full bg-health-600 text-white px-4 py-2 rounded-lg hover:bg-health-700 transition-colors text-center font-medium">
                            <i class="fas fa-calendar-plus mr-2"></i>Schedule Follow-up
                        </a>
                        <a href="{% url 'api:prescription_create_for_record' medical_record.id %}" class="block w-full bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-center font-medium">
                            <i class="fas fa-pills mr-2"></i>Add Prescription
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
