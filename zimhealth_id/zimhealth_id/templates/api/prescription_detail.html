{% extends 'base.html' %}

{% block title %}Prescription - {{ prescription.medication }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:prescriptions' %}" class="text-medical-600 hover:text-medical-700 transition-colors">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Prescription Details</h1>
                            <p class="text-gray-600 mt-1">{{ prescription.medication }} for {{ prescription.patient.full_name }}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        {% if prescription.status == 'active' %}
                        <button onclick="updatePrescriptionStatus('completed')" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium">
                            <i class="fas fa-check mr-2"></i>Mark Completed
                        </button>
                        <button onclick="updatePrescriptionStatus('discontinued')" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors font-medium">
                            <i class="fas fa-stop mr-2"></i>Discontinue
                        </button>
                        {% endif %}
                        <button onclick="window.print()" class="bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700 transition-colors font-medium">
                            <i class="fas fa-print mr-2"></i>Print
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Prescription Information -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-bold text-gray-900">Prescription Information</h3>
                        <span class="px-3 py-1 text-sm font-medium rounded-full
                            {% if prescription.status == 'active' %}bg-green-100 text-green-800
                            {% elif prescription.status == 'completed' %}bg-blue-100 text-blue-800
                            {% elif prescription.status == 'discontinued' %}bg-red-100 text-red-800
                            {% elif prescription.status == 'on_hold' %}bg-yellow-100 text-yellow-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {{ prescription.get_status_display }}
                        </span>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-6">
                        <div>
                            <p class="text-sm font-medium text-gray-500 mb-1">Medication</p>
                            <p class="text-2xl font-bold text-gray-900">{{ prescription.medication }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500 mb-1">Dosage</p>
                            <p class="text-lg font-semibold text-gray-900">{{ prescription.dosage }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500 mb-1">Frequency</p>
                            <p class="text-lg font-semibold text-gray-900">{{ prescription.get_frequency_display }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500 mb-1">Duration</p>
                            <p class="text-lg font-semibold text-gray-900">{{ prescription.duration }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500 mb-1">Start Date</p>
                            <p class="text-lg font-semibold text-gray-900">{{ prescription.start_date|date:"M d, Y" }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500 mb-1">End Date</p>
                            <p class="text-lg font-semibold text-gray-900">
                                {% if prescription.end_date %}
                                    {{ prescription.end_date|date:"M d, Y" }}
                                {% else %}
                                    Not specified
                                {% endif %}
                            </p>
                        </div>
                        {% if prescription.quantity_prescribed %}
                        <div>
                            <p class="text-sm font-medium text-gray-500 mb-1">Quantity</p>
                            <p class="text-lg font-semibold text-gray-900">{{ prescription.quantity_prescribed }}</p>
                        </div>
                        {% endif %}
                        <div>
                            <p class="text-sm font-medium text-gray-500 mb-1">Refills Allowed</p>
                            <p class="text-lg font-semibold text-gray-900">{{ prescription.refills_allowed }}</p>
                        </div>
                    </div>

                    {% if prescription.instructions %}
                    <div class="mt-6">
                        <p class="text-sm font-medium text-gray-500 mb-2">Instructions</p>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-800">{{ prescription.instructions }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Patient Information -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Patient Information</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Full Name</p>
                            <p class="text-lg font-semibold text-gray-900">{{ prescription.patient.full_name }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">ZimHealth ID</p>
                            <p class="text-lg font-semibold text-gray-900">{{ prescription.patient.zimhealth_id }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Age</p>
                            <p class="text-lg font-semibold text-gray-900">{{ prescription.patient.age }} years</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Phone</p>
                            <p class="text-lg font-semibold text-gray-900">{{ prescription.patient.phone_number }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Blood Type</p>
                            <p class="text-lg font-semibold text-gray-900">{{ prescription.patient.blood_type|default:"Unknown" }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Emergency Contact</p>
                            <p class="text-lg font-semibold text-gray-900">{{ prescription.patient.emergency_contact }}</p>
                        </div>
                    </div>

                    {% if prescription.patient.allergies %}
                    <div class="mt-4">
                        <p class="text-sm font-medium text-gray-500 mb-2">Allergies</p>
                        <div class="flex flex-wrap gap-2">
                            {% for allergy in prescription.patient.allergies %}
                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">{{ allergy }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Medical Record Information -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Related Medical Record</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Date</p>
                            <p class="text-lg font-semibold text-gray-900">{{ prescription.medical_record.date|date:"M d, Y" }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Doctor</p>
                            <p class="text-lg font-semibold text-gray-900">{{ prescription.medical_record.doctor_name }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Facility</p>
                            <p class="text-lg font-semibold text-gray-900">{{ prescription.medical_record.facility_name }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Prescribed By</p>
                            <p class="text-lg font-semibold text-gray-900">
                                {% if prescription.prescribed_by %}
                                    {{ prescription.prescribed_by.get_full_name|default:prescription.prescribed_by.username }}
                                {% else %}
                                    {{ prescription.medical_record.doctor_name }}
                                {% endif %}
                            </p>
                        </div>
                    </div>

                    <div class="mt-4">
                        <p class="text-sm font-medium text-gray-500 mb-2">Diagnosis</p>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-800">{{ prescription.medical_record.diagnosis }}</p>
                        </div>
                    </div>

                    <div class="mt-4">
                        <a href="{% url 'api:medical_record_detail' prescription.medical_record.id %}" class="text-medical-600 hover:text-medical-700 font-medium">
                            <i class="fas fa-external-link-alt mr-1"></i>View Full Medical Record
                        </a>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Status Actions -->
                {% if prescription.status == 'active' %}
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Update Status</h3>
                    <div class="space-y-3">
                        <button onclick="updatePrescriptionStatus('completed')" class="block w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium">
                            <i class="fas fa-check mr-2"></i>Mark Completed
                        </button>
                        <button onclick="updatePrescriptionStatus('on_hold')" class="block w-full bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors font-medium">
                            <i class="fas fa-pause mr-2"></i>Put On Hold
                        </button>
                        <button onclick="updatePrescriptionStatus('discontinued')" class="block w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors font-medium">
                            <i class="fas fa-stop mr-2"></i>Discontinue
                        </button>
                    </div>
                </div>
                {% elif prescription.status == 'on_hold' %}
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Resume Prescription</h3>
                    <button onclick="updatePrescriptionStatus('active')" class="block w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium">
                        <i class="fas fa-play mr-2"></i>Resume
                    </button>
                </div>
                {% endif %}

                <!-- Quick Actions -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="{% url 'api:patient_detail' prescription.patient.zimhealth_id %}" class="block w-full bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700 transition-colors text-center font-medium">
                            <i class="fas fa-user mr-2"></i>View Patient
                        </a>
                        <a href="{% url 'api:medical_record_detail' prescription.medical_record.id %}" class="block w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center font-medium">
                            <i class="fas fa-file-medical-alt mr-2"></i>View Medical Record
                        </a>
                        <button onclick="window.print()" class="block w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors font-medium">
                            <i class="fas fa-print mr-2"></i>Print Prescription
                        </button>
                    </div>
                </div>

                <!-- Prescription Timeline -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Timeline</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <div>
                                <p class="text-sm font-medium">Prescribed</p>
                                <p class="text-xs text-gray-500">{{ prescription.created_at|date:"M d, Y H:i" }}</p>
                            </div>
                        </div>
                        {% if prescription.start_date %}
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <div>
                                <p class="text-sm font-medium">Start Date</p>
                                <p class="text-xs text-gray-500">{{ prescription.start_date|date:"M d, Y" }}</p>
                            </div>
                        </div>
                        {% endif %}
                        {% if prescription.end_date %}
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                            <div>
                                <p class="text-sm font-medium">End Date</p>
                                <p class="text-xs text-gray-500">{{ prescription.end_date|date:"M d, Y" }}</p>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updatePrescriptionStatus(status) {
    fetch(`/api/prescriptions/{{ prescription.id }}/update-status/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: `status=${status}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to update prescription status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred');
    });
}
</script>
{% endblock %}
