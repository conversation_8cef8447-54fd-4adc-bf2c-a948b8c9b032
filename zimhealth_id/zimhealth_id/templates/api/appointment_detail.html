{% extends 'base.html' %}

{% block title %}Appointment - {{ appointment.patient.full_name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="{% url 'api:appointments' %}" class="text-medical-600 hover:text-medical-700 transition-colors">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </a>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Appointment Details</h1>
                            <p class="text-gray-600 mt-1">{{ appointment.patient.full_name }} - {{ appointment.date|date:"M d, Y" }}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        {% if appointment.status == 'scheduled' %}
                        <button onclick="updateAppointmentStatus('completed')" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium">
                            <i class="fas fa-check mr-2"></i>Mark Completed
                        </button>
                        <button onclick="updateAppointmentStatus('cancelled')" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors font-medium">
                            <i class="fas fa-times mr-2"></i>Cancel
                        </button>
                        {% endif %}
                        <a href="{% url 'api:medical_record_create_for_patient' appointment.patient.zimhealth_id %}" class="bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700 transition-colors font-medium">
                            <i class="fas fa-file-medical-alt mr-2"></i>Create Record
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Appointment Information -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-bold text-gray-900">Appointment Information</h3>
                        <span class="px-3 py-1 text-sm font-medium rounded-full
                            {% if appointment.status == 'scheduled' %}bg-blue-100 text-blue-800
                            {% elif appointment.status == 'completed' %}bg-green-100 text-green-800
                            {% elif appointment.status == 'cancelled' %}bg-red-100 text-red-800
                            {% elif appointment.status == 'no_show' %}bg-gray-100 text-gray-800
                            {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                            {{ appointment.get_status_display }}
                        </span>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-6">
                        <div>
                            <p class="text-sm font-medium text-gray-500 mb-1">Date & Time</p>
                            <p class="text-lg font-semibold text-gray-900">{{ appointment.date|date:"M d, Y" }}</p>
                            <p class="text-md text-gray-700">{{ appointment.time|time:"H:i" }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500 mb-1">Type</p>
                            <p class="text-lg font-semibold text-gray-900">{{ appointment.get_appointment_type_display }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500 mb-1">Doctor</p>
                            <p class="text-lg font-semibold text-gray-900">{{ appointment.doctor_name }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500 mb-1">Facility</p>
                            <p class="text-lg font-semibold text-gray-900">{{ appointment.facility_name|default:"Not specified" }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500 mb-1">Priority</p>
                            <p class="text-lg font-semibold text-gray-900">{{ appointment.get_priority_display|default:"Normal" }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500 mb-1">Duration</p>
                            <p class="text-lg font-semibold text-gray-900">{{ appointment.duration|default:"30" }} minutes</p>
                        </div>
                    </div>

                    {% if appointment.reason %}
                    <div class="mt-6">
                        <p class="text-sm font-medium text-gray-500 mb-2">Reason for Visit</p>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-800">{{ appointment.reason }}</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if appointment.notes %}
                    <div class="mt-6">
                        <p class="text-sm font-medium text-gray-500 mb-2">Notes</p>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-800">{{ appointment.notes }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Patient Information -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Patient Information</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Full Name</p>
                            <p class="text-lg font-semibold text-gray-900">{{ appointment.patient.full_name }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">ZimHealth ID</p>
                            <p class="text-lg font-semibold text-gray-900">{{ appointment.patient.zimhealth_id }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Age</p>
                            <p class="text-lg font-semibold text-gray-900">{{ appointment.patient.age }} years</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Phone</p>
                            <p class="text-lg font-semibold text-gray-900">{{ appointment.patient.phone_number }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Blood Type</p>
                            <p class="text-lg font-semibold text-gray-900">{{ appointment.patient.blood_type|default:"Unknown" }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Emergency Contact</p>
                            <p class="text-lg font-semibold text-gray-900">{{ appointment.patient.emergency_contact }}</p>
                        </div>
                    </div>

                    {% if appointment.patient.allergies %}
                    <div class="mt-4">
                        <p class="text-sm font-medium text-gray-500 mb-2">Allergies</p>
                        <div class="flex flex-wrap gap-2">
                            {% for allergy in appointment.patient.allergies %}
                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">{{ allergy }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Status Actions -->
                {% if appointment.status == 'scheduled' %}
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Update Status</h3>
                    <div class="space-y-3">
                        <button onclick="updateAppointmentStatus('completed')" class="block w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium">
                            <i class="fas fa-check mr-2"></i>Mark Completed
                        </button>
                        <button onclick="updateAppointmentStatus('no_show')" class="block w-full bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors font-medium">
                            <i class="fas fa-user-times mr-2"></i>No Show
                        </button>
                        <button onclick="showCancelModal()" class="block w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors font-medium">
                            <i class="fas fa-times mr-2"></i>Cancel
                        </button>
                    </div>
                </div>
                {% endif %}

                <!-- Quick Actions -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="{% url 'api:patient_detail' appointment.patient.zimhealth_id %}" class="block w-full bg-medical-600 text-white px-4 py-2 rounded-lg hover:bg-medical-700 transition-colors text-center font-medium">
                            <i class="fas fa-user mr-2"></i>View Patient
                        </a>
                        <a href="{% url 'api:medical_record_create_for_patient' appointment.patient.zimhealth_id %}" class="block w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center font-medium">
                            <i class="fas fa-file-medical-alt mr-2"></i>Create Medical Record
                        </a>
                        <a href="{% url 'api:appointment_create_for_patient' appointment.patient.zimhealth_id %}" class="block w-full bg-health-600 text-white px-4 py-2 rounded-lg hover:bg-health-700 transition-colors text-center font-medium">
                            <i class="fas fa-calendar-plus mr-2"></i>Schedule Follow-up
                        </a>
                    </div>
                </div>

                <!-- Appointment History -->
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">System Info</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Created</span>
                            <span class="font-medium">{{ appointment.created_at|date:"M d, Y H:i" }}</span>
                        </div>
                        {% if appointment.updated_at != appointment.created_at %}
                        <div class="flex justify-between">
                            <span class="text-gray-600">Updated</span>
                            <span class="font-medium">{{ appointment.updated_at|date:"M d, Y H:i" }}</span>
                        </div>
                        {% endif %}
                        {% if appointment.created_by %}
                        <div class="flex justify-between">
                            <span class="text-gray-600">Created By</span>
                            <span class="font-medium">{{ appointment.created_by.get_full_name|default:appointment.created_by.username }}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cancel Modal -->
<div id="cancelModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-bold text-gray-900 mb-4">Cancel Appointment</h3>
        <div class="mb-4">
            <label for="cancellationReason" class="block text-sm font-medium text-gray-700 mb-2">Reason for cancellation</label>
            <textarea id="cancellationReason" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-medical-500 focus:border-medical-500" placeholder="Enter reason for cancellation..."></textarea>
        </div>
        <div class="flex justify-end space-x-3">
            <button onclick="hideCancelModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">Cancel</button>
            <button onclick="confirmCancel()" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">Confirm Cancel</button>
        </div>
    </div>
</div>

<script>
function updateAppointmentStatus(status) {
    fetch(`/api/appointments/{{ appointment.id }}/update-status/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: `status=${status}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to update appointment status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred');
    });
}

function showCancelModal() {
    document.getElementById('cancelModal').classList.remove('hidden');
    document.getElementById('cancelModal').classList.add('flex');
}

function hideCancelModal() {
    document.getElementById('cancelModal').classList.add('hidden');
    document.getElementById('cancelModal').classList.remove('flex');
}

function confirmCancel() {
    const reason = document.getElementById('cancellationReason').value;
    
    fetch(`/api/appointments/{{ appointment.id }}/update-status/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: `status=cancelled&reason=${encodeURIComponent(reason)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to cancel appointment');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred');
    });
}
</script>
{% endblock %}
