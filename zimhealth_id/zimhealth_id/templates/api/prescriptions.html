{% extends 'base.html' %}
{% load static %}

{% block title %}Prescription Management - ZimHealth-ID{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'assets/css/dashboard.css' %}">
<link rel="stylesheet" href="{% static 'assets/css/prescriptions.css' %}">
{% endblock %}

{% block content %}
<div class="prescriptions-container">
    <!-- Professional Prescriptions Header -->
    <div class="prescriptions-header">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Prescription Management</h1>
                        <p class="text-gray-600 mt-1 text-sm">Professional medication management and prescription tracking system</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="new-prescription-button flex items-center space-x-2">
                            <i class="fas fa-prescription-bottle-alt"></i>
                            <span>New Prescription</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Content with Professional Stats -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <!-- Professional Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon active">
                        <i class="fas fa-pills text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Active Prescriptions</p>
                        <p class="text-2xl font-bold text-gray-900">{{ active_prescriptions|default:42 }}</p>
                    </div>
                </div>
            </div>

            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon expiring">
                        <i class="fas fa-exclamation-triangle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Expiring Soon</p>
                        <p class="text-2xl font-bold text-gray-900">{{ expiring_prescriptions|default:8 }}</p>
                    </div>
                </div>
            </div>

            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon completed">
                        <i class="fas fa-check-circle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Completed</p>
                        <p class="text-2xl font-bold text-gray-900">{{ completed_prescriptions|default:127 }}</p>
                    </div>
                </div>
            </div>

            <div class="stats-card p-6">
                <div class="flex items-center">
                    <div class="stats-icon discontinued">
                        <i class="fas fa-ban text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Discontinued</p>
                        <p class="text-2xl font-bold text-gray-900">{{ discontinued_prescriptions|default:15 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Government-Level Professional Search Controls -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <!-- Left: Enhanced Search Bar with QR Scanner -->
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <input type="text" id="prescription-search" name="search"
                               class="prescriptions-search-input w-full pl-10 pr-12 py-2.5"
                               placeholder="Search prescriptions by medication, patient, or use QR scanner...">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-500"></i>
                        </div>
                        <!-- QR Code Scanner inside search bar -->
                        <button type="button" id="prescription-qr-scanner-btn" class="prescriptions-search-qr-button absolute inset-y-0 right-0 pr-3 flex items-center" title="Scan QR Code">
                            <i class="fas fa-qrcode"></i>
                        </button>
                    </div>
                </div>

                <!-- Right: Filter Controls -->
                <div class="flex items-center space-x-3">
                    <!-- Status Filter -->
                    <select id="status-filter" name="status" class="prescriptions-filter-button">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="completed">Completed</option>
                        <option value="discontinued">Discontinued</option>
                        <option value="on_hold">On Hold</option>
                    </select>

                    <!-- Frequency Filter -->
                    <select id="frequency-filter" name="frequency" class="prescriptions-filter-button">
                        <option value="">All Frequencies</option>
                        <option value="once_daily">Once Daily</option>
                        <option value="twice_daily">Twice Daily</option>
                        <option value="three_times_daily">Three Times Daily</option>
                        <option value="as_needed">As Needed</option>
                    </select>

                    <!-- Date Filter -->
                    <input type="date" id="date-filter" class="prescriptions-filter-button" title="Start Date">
                </div>
            </div>

            <!-- Results Count -->
            <div class="mt-3 flex items-center justify-between">
                <div class="results-count text-sm text-gray-600 font-medium">
                    Loading prescriptions...
                </div>
                <div class="text-xs text-gray-500">
                    Professional Medication Management System
                </div>
            </div>
        </div>

        <!-- Professional Prescriptions Management -->
        <div class="prescriptions-table-card">
            <div class="prescriptions-table-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Healthcare Prescriptions Registry</h3>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <span>{{ prescriptions.count|default:"3" }} prescriptions</span>
                    </div>
                </div>
            </div>

            <!-- Professional Prescriptions Table -->
            <div class="prescriptions-list">
                <div class="overflow-x-auto">
                    <table class="prescriptions-table">
                        <thead>
                            <tr>
                                <th class="medication-column">Medication</th>
                                <th class="patient-column">Patient</th>
                                <th class="dosage-column">Dosage</th>
                                <th class="frequency-column">Frequency</th>
                                <th class="status-column">Status</th>
                                <th class="doctor-column">Prescribed By</th>
                                <th class="actions-column">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for prescription in prescriptions %}
                                <tr>
                                    <!-- Medication Column -->
                                    <td class="medication-column">
                                        <div class="flex items-center space-x-3">
                                            <div class="prescription-status-indicator {{ prescription.status|default:'active' }}"></div>
                                            <div class="prescription-avatar">
                                                <i class="fas fa-pills"></i>
                                            </div>
                                            <div>
                                                <div class="text-sm font-semibold text-gray-900">{{ prescription.medication|default:"Metformin 500mg" }}</div>
                                                <div class="text-xs text-gray-500">ID: {{ prescription.id|default:"RX-001" }}</div>
                                            </div>
                                        </div>
                                    </td>

                                    <!-- Patient Column -->
                                    <td class="patient-column">
                                        <div class="text-sm font-medium text-gray-900">{{ prescription.medical_record.patient.full_name|default:"Sarah Johnson" }}</div>
                                        <div class="text-xs text-gray-500">{{ prescription.medical_record.patient.zimhealth_id|default:"ZH-2024-001" }}</div>
                                    </td>

                                    <!-- Dosage Column -->
                                    <td class="dosage-column">
                                        <div class="text-sm font-medium text-gray-900">{{ prescription.dosage|default:"500mg" }}</div>
                                    </td>

                                    <!-- Frequency Column -->
                                    <td class="frequency-column">
                                        <span class="frequency-badge">{{ prescription.get_frequency_display|default:"Twice Daily" }}</span>
                                    </td>

                                    <!-- Status Column -->
                                    <td class="status-column">
                                        <span class="prescription-status-badge {{ prescription.status|default:'active' }}">{{ prescription.get_status_display|default:"Active" }}</span>
                                    </td>

                                    <!-- Doctor Column -->
                                    <td class="doctor-column">
                                        <div class="text-sm font-medium text-gray-900">{{ prescription.medical_record.doctor_name|default:"Dr. Smith" }}</div>
                                        <div class="text-xs text-gray-500">{{ prescription.medical_record.facility_name|default:"Central Hospital" }}</div>
                                    </td>

                                    <!-- Actions Column -->
                                    <td class="actions-column">
                                        <div class="flex items-center justify-center space-x-1">
                                            <button class="prescription-action-button view" data-action="view" data-prescription-id="{{ prescription.id|default:'RX-001' }}" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            {% if prescription.status == 'active' or not prescription.status %}
                                                <button class="prescription-action-button complete" data-action="complete" data-prescription-id="{{ prescription.id|default:'RX-001' }}" title="Mark Complete">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="prescription-action-button hold" data-action="hold" data-prescription-id="{{ prescription.id|default:'RX-001' }}" title="Put on Hold">
                                                    <i class="fas fa-clock"></i>
                                                </button>
                                                <button class="prescription-action-button discontinue" data-action="discontinue" data-prescription-id="{{ prescription.id|default:'RX-001' }}" title="Discontinue">
                                                    <i class="fas fa-ban"></i>
                                                </button>
                                            {% endif %}
                                            <button class="prescription-action-button print" data-action="print" data-prescription-id="{{ prescription.id|default:'RX-001' }}" title="Print">
                                                <i class="fas fa-print"></i>
                                            </button>
                                            <button class="prescription-action-button edit" data-action="edit" data-prescription-id="{{ prescription.id|default:'RX-001' }}" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Prescription Details Row (if expanded) -->
                                {% if prescription.instructions or prescription.duration %}
                                    <tr class="details-row">
                                        <td colspan="7" class="px-6 py-3 bg-gray-50 border-b border-gray-200">
                                            {% if prescription.instructions %}
                                                <p class="text-xs text-gray-600 mb-1"><strong>Instructions:</strong> {{ prescription.instructions }}</p>
                                            {% endif %}
                                            {% if prescription.duration %}
                                                <p class="text-xs text-gray-600 mb-1"><strong>Duration:</strong> {{ prescription.duration }}</p>
                                            {% endif %}
                                            {% if prescription.start_date %}
                                                <p class="text-xs text-gray-600"><strong>Start Date:</strong> {{ prescription.start_date|date:"M d, Y" }}</p>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endif %}
                            {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center py-12">
                                        <div class="flex flex-col items-center">
                                            <div class="prescription-avatar mx-auto mb-4">
                                                <i class="fas fa-prescription-bottle-alt text-gray-400 text-2xl"></i>
                                            </div>
                                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No prescriptions found</h3>
                                            <p class="text-gray-500 mb-4">Get started by creating your first prescription or use the search filters.</p>
                                            <button class="new-prescription-button">
                                                <i class="fas fa-plus mr-2"></i>New Prescription
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Enhanced Pagination -->
            {% if prescriptions.has_other_pages %}
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-500">
                            Showing {{ prescriptions.start_index }} to {{ prescriptions.end_index }} of {{ prescriptions.paginator.count }} results
                        </div>
                        <div class="flex items-center space-x-2">
                            {% if prescriptions.has_previous %}
                                <a href="?page={{ prescriptions.previous_page_number }}"
                                   class="pagination-button">
                                    Previous
                                </a>
                            {% endif %}

                            <span class="px-3 py-2 text-sm font-medium text-gray-700">
                                Page {{ prescriptions.number }} of {{ prescriptions.paginator.num_pages }}
                            </span>

                            {% if prescriptions.has_next %}
                                <a href="?page={{ prescriptions.next_page_number }}"
                                   class="pagination-button">
                                    Next
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'assets/js/prescriptions.js' %}"></script>
{% endblock %}
